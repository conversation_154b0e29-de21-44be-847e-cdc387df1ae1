import { AgentState } from './types';

// モックデータ
export const mockAgentResult: AgentState = {
  meetingNotes: '',
  extractedInfo: {
    companyName: '株式会社サンプル',
    mainTopic: 'DX推進プロジェクト',
    currentChallenges: [
      {
        category: '業務効率',
        description: '手作業による定型業務が多く、生産性が低い',
        impact: 'high'
      },
      {
        category: 'データ管理',
        description: 'データが分散しており、一元管理ができていない',
        impact: 'medium'
      }
    ],
    idealState: [
      '業務の自動化により生産性を30%向上',
      'データの一元管理による意思決定の迅速化'
    ],
    proposedSolutions: [
      {
        problemCategory: '業務効率',
        solution: 'RPA導入による自動化',
        benefits: ['作業時間80%削減', 'ヒューマンエラー防止']
      },
      {
        problemCategory: 'データ管理',
        solution: 'クラウド基盤の構築',
        benefits: ['リアルタイムデータ分析', '部門間連携強化']
      }
    ],
    roi: {
      investment: [
        { item: 'システム開発費', amount: 50000000 },
        { item: 'コンサルティング費', amount: 10000000 }
      ],
      returns: [
        { item: '人件費削減', amount: 40000000, timeframe: '年間' },
        { item: '業務効率化による収益増', amount: 60000000, timeframe: '年間' }
      ],
      percentage: 167
    },
    timeline: [
      { date: '2025年6月', title: 'プロジェクト開始', description: '要件定義' },
      { date: '2025年9月', title: 'システム開発', description: '開発・テスト' },
      { date: '2025年12月', title: '本番稼働', description: '運用開始' }
    ],
    nextSteps: [
      {
        title: '予算承認',
        description: '経営会議での予算承認を取得',
        deadline: '2025年5月末',
        priority: 'high'
      },
      {
        title: 'チーム編成',
        description: 'プロジェクトチームの編成',
        deadline: '2025年6月15日',
        priority: 'high'
      }
    ]
  },
  slideStructure: [
    {
      slideType: 'cover',
      component: 'CoverSlide',
      props: {
        companyName: '株式会社サンプル',
        mainTitle: 'DX推進プロジェクト提案書',
        subTitle: '業務効率化とデータ活用による成長戦略',
        date: new Date().toLocaleDateString('ja-JP')
      },
      order: 1
    },
    {
      slideType: 'executiveSummary',
      component: 'ExecutiveSummarySlide',
      props: {
        label: 'エグゼクティブサマリー',
        keyMessage: 'DXによる競争力強化',
        sections: [
          {
            title: '現状の課題',
            points: ['手作業による業務効率の低下', 'データの分散管理'],
            icon: '⚠️'
          },
          {
            title: '提案ソリューション',
            points: ['RPA導入による自動化', 'クラウド基盤の構築'],
            icon: '💡'
          },
          {
            title: '期待効果',
            points: ['生産性30%向上', 'ROI 167%'],
            icon: '📈'
          }
        ]
      },
      order: 2
    },
    {
      slideType: 'roi',
      component: 'ROICalculationSlide',
      props: {
        label: 'ROI計算',
        keyMessage: 'DX投資による高いリターン',
        investment: [
          { item: 'システム開発費', amount: 50000000 },
          { item: 'コンサルティング費', amount: 10000000 }
        ],
        returns: [
          { item: '人件費削減', amount: 40000000, timeframe: '年間' },
          { item: '業務効率化による収益増', amount: 60000000, timeframe: '年間' }
        ],
        totalROI: {
          percentage: 167,
          paybackPeriod: '1.2年'
        },
        notes: ['初期投資は6,000万円', '年間リターンは1億円を想定']
      },
      order: 3
    }
  ],
  generatedSlides: [
    {
      component: 'CoverSlide',
      props: {
        companyName: '株式会社サンプル',
        mainTitle: 'DX推進プロジェクト提案書',
        subTitle: '業務効率化とデータ活用による成長戦略',
        date: new Date().toLocaleDateString('ja-JP')
      }
    },
    {
      component: 'ExecutiveSummarySlide',
      props: {
        label: 'エグゼクティブサマリー',
        keyMessage: 'DXによる競争力強化',
        sections: [
          {
            title: '現状の課題',
            points: ['手作業による業務効率の低下', 'データの分散管理'],
            icon: '⚠️'
          },
          {
            title: '提案ソリューション',
            points: ['RPA導入による自動化', 'クラウド基盤の構築'],
            icon: '💡'
          },
          {
            title: '期待効果',
            points: ['生産性30%向上', 'ROI 167%'],
            icon: '📈'
          }
        ]
      }
    },
    {
      component: 'ROICalculationSlide',
      props: {
        label: 'ROI計算',
        keyMessage: 'DX投資による高いリターン',
        investment: [
          { item: 'システム開発費', amount: 50000000 },
          { item: 'コンサルティング費', amount: 10000000 }
        ],
        returns: [
          { item: '人件費削減', amount: 40000000, timeframe: '年間' },
          { item: '業務効率化による収益増', amount: 60000000, timeframe: '年間' }
        ],
        totalROI: {
          percentage: 167,
          paybackPeriod: '1.2年'
        },
        notes: ['初期投資は6,000万円', '年間リターンは1億円を想定']
      }
    }
  ],
  errors: []
};
