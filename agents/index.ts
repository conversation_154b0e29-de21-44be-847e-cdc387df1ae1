import { AgentState } from './types';

export type { AgentState };

// エクスポート用のインターフェース
export interface SlideGeneratorResult {
  slides: Array<{
    component: string;
    props: any;
  }>;
  errors: string[];
}

// ヘルパー関数：AgentStateからSlideGeneratorResultに変換
export function processAgentResult(state: AgentState): SlideGeneratorResult {
  return {
    slides: state.generatedSlides,
    errors: state.errors
  };
}
