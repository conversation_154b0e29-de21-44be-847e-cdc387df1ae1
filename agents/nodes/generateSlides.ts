import { ChatOpenAI } from '@langchain/openai';
import { GenerateSlidesInput, GenerateSlidesOutput } from '../types';

const GENERATE_SLIDES_PROMPT = `
あなたはスライドコンテンツ生成の専門家です。
スライド構成案と抽出された情報を基に、実際のスライドコンテンツを生成してください。

各スライドのpropsを適切に設定し、見やすく効果的なプレゼンテーションになるようにしてください。

日本語で生成し、ビジネスプレゼンテーションに適した表現を使用してください。
`;

export class GenerateSlidesAgent {
  private model: ChatOpenAI;

  constructor() {
    this.model = new ChatOpenAI({
      modelName: 'gpt-4o',  // より高性能なモデルに変更
      temperature: 0.1,     // 出力の安定性を向上
      timeout: 60000        // タイムアウトを設定
    });
  }

  async execute(input: GenerateSlidesInput): Promise<GenerateSlidesOutput> {
    try {
      console.log('GenerateSlidesAgent: スライド生成を開始します');
      const generatedSlides = [];

      for (const slide of input.slideStructure) {
        console.log(`GenerateSlidesAgent: ${slide.component}の生成を開始します`);
        const prompt = `
          以下のスライドのコンテンツを生成してください：
          
          コンポーネント: ${slide.component}
          スライドタイプ: ${slide.slideType}
          
          基本となるprops:
          ${JSON.stringify(slide.props, null, 2)}
          
          抽出された関連情報:
          ${JSON.stringify(input.extractedInfo, null, 2)}
          
          ${slide.component === 'ROICalculationSlide' ? `
          【ROICalculationSlide専用指示】
          このスライドでは以下の形式でデータを生成してください：
          - investment: 配列形式で、各要素は {item: "項目名", amount: 数値, unit?: "単位"}
          - returns: 配列形式で、各要素は {item: "項目名", amount: 数値, unit?: "単位", timeframe?: "期間"}
          - totalROI: {percentage: 数値, paybackPeriod?: "期間"}
          - notes: 文字列の配列（オプション）
          
          必ず具体的な数値を設定し、空の配列は避けてください。
          ` : ''}
          
          ${slide.component === 'ProblemAnalysisSlide' ? `
          【ProblemAnalysisSlide専用指示】
          このスライドでは以下の形式でデータを生成してください：
          - problems: 配列形式で、各要素は {category: "カテゴリ", description: "説明", impact: "high"|"medium"|"low", details?: 文字列配列}
          - futureProjection: {title: "タイトル", description: "説明"} （オプション）
          
          currentChallenges, idealStateなどのフィールド名は使用せず、必ずproblems, futureProjectionを使用してください。
          ` : ''}
          
          ${slide.component === 'ExecutiveSummarySlide' ? `
          【ExecutiveSummarySlide専用指示】
          このスライドでは以下の形式でデータを生成してください：
          - sections: 配列形式で、各要素は {title: "タイトル", points: 文字列配列, icon?: "アイコン"}
          
          sectionsフィールドは必須です。空の配列は避けてください。
          ` : ''}
          
          ${slide.component === 'AsIsSlide' ? `
          【AsIsSlide専用指示】
          このスライドでは以下の形式でデータを生成してください：
          - categories: 文字列の配列（カテゴリ名）
          - asIs: 文字列の2次元配列（現状の説明、各カテゴリに対応）
          - toBe: 文字列の2次元配列（理想状態の説明、各カテゴリに対応）
          
          currentChallenges, idealStateなどのフィールド名は使用せず、必ずcategories, asIs, toBeを使用してください。
          ` : ''}
          
          ${slide.component === 'SolutionOverviewSlide' ? `
          【SolutionOverviewSlide専用指示】
          このスライドでは以下の形式でデータを生成してください：
          - solutions: 配列形式で、各要素は {problemCategory: "カテゴリ", solution: "解決策", benefits: 文字列配列}
          - centralConcept: 中心概念（オプション）
          
          proposedSolutionsなどのフィールド名は使用せず、必ずsolutionsを使用してください。
          benefitsは必ず文字列の配列にしてください。
          ` : ''}
          
          ${slide.component === 'CoverSlide' ? `
          【CoverSlide専用指示】
          このスライドでは以下の形式でデータを生成してください：
          - companyName: 会社名（文字列）
          - mainTitle: メインタイトル（文字列）
          - subTitle: サブタイトル（文字列）
          - date: 日付（文字列）
          
          title, subtitleなどのフィールド名は使用せず、必ずmainTitle, subTitleを使用してください。
          ` : ''}
          
          【重要】必ずJSON形式のpropsオブジェクトのみを返してください。
          説明文やMarkdownは含めず、以下の形式で返してください：
          
          {
            "label": "スライドラベル",
            "keyMessage": "キーメッセージ",
            ...その他のプロパティ
          }
          
          このスライドに適したコンテンツを生成し、propsを完成させてください。
          日本語で、プロフェッショナルな表現を使用してください。
        `;

        const response = await this.model.invoke([
          {
            role: 'system',
            content: GENERATE_SLIDES_PROMPT
          },
          {
            role: 'user',
            content: prompt
          }
        ]);

        console.log(`GenerateSlidesAgent: ${slide.component}のAI応答を受信しました`);
        // レスポンスからpropsを抽出
        const content = response.content as string;
        let props;
        try {
          // 複数パターンでJSON抽出を試みる
          let jsonStr = '';
          
          // パターン1: ```json ... ``` 
          const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
          if (jsonMatch) {
            jsonStr = jsonMatch[1];
          } 
          // パターン2: 全体がJSONの場合（直接パース）
          else if (content.trim().startsWith('{')) {
            jsonStr = content.trim();
          }
          // パターン3: { ... } の最初のJSONオブジェクト（より柔軟に）
          else {
            const startIndex = content.indexOf('{');
            if (startIndex !== -1) {
              let braceCount = 0;
              let endIndex = -1;
              
              for (let i = startIndex; i < content.length; i++) {
                if (content[i] === '{') {
                  braceCount++;
                } else if (content[i] === '}') {
                  braceCount--;
                  if (braceCount === 0) {
                    endIndex = i;
                    break;
                  }
                }
              }
              
              if (endIndex !== -1) {
                jsonStr = content.substring(startIndex, endIndex + 1);
              }
            }
          }
          
          if (jsonStr) {
            props = JSON.parse(jsonStr.trim());
            console.log(`GenerateSlidesAgent: ${slide.component}のJSON解析に成功しました`);
          } else {
            throw new Error('JSON not found in response');
          }
        } catch (e) {
          console.error(`GenerateSlidesAgent: ${slide.component}のJSON解析に失敗しました:`, e);
          console.error(`GenerateSlidesAgent: ${slide.component}の元の応答:`, content);
          
          // JSONパースが失敗した場合、直接contentをJSONとしてパースを試みる
          try {
            console.log(`GenerateSlidesAgent: ${slide.component}の直接パースを試行します`);
            props = JSON.parse(content.trim());
            console.log(`GenerateSlidesAgent: ${slide.component}の直接パースに成功しました`);
          } catch (directParseError) {
            console.error(`GenerateSlidesAgent: ${slide.component}の直接パースも失敗しました:`, directParseError);
            
            // フォールバック：基本propsまたはデフォルト値
            props = slide.props || {
              title: `${slide.slideType}スライド`,
              content: '内容を生成中...'
            };
            console.log(`GenerateSlidesAgent: ${slide.component}にフォールバック値を使用します`);
          }
        }

        generatedSlides.push({
          component: slide.component,
          props: props
        });
        console.log(`GenerateSlidesAgent: ${slide.component}の生成が完了しました`);
      }

      console.log('GenerateSlidesAgent: すべてのスライドの生成が完了しました');
      return {
        generatedSlides
      };
    } catch (error) {
      console.error('GenerateSlidesAgent: エラーが発生しました:', error);
      throw error;
    }
  }
}
