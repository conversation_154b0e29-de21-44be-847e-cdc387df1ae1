import { ChatOpenAI } from '@langchain/openai';
import { z } from 'zod';
import { slideStructureSchema } from '../schemas';
import { PlanSlidesInput, PlanSlidesOutput } from '../types';
import { 
  CORE_SLIDES, 
  OPTIONAL_SLIDES, 
  ALL_SLIDE_TEMPLATES,
  SlideTemplate 
} from '../templates/slideTemplate';

const PLAN_SLIDES_PROMPT = `
あなたはプレゼンテーション構成の専門家です。
抽出された情報を基に、効果的なスライド構成を計画してください。

【ハイブリッド方式】
1. 必須スライド（CORE）: 必ず含める
2. オプションスライド（OPTIONAL）: 内容に応じて選択

=== 必須スライド（必ず含める） ===
{coreSlides}

=== オプションスライド（内容に応じて選択） ===
{optionalSlides}

【選択基準】
- 抽出された情報に基づいて、効果的なプレゼンテーションになるオプションスライドを選択
- 各スライドの「条件」を参考に、含めるべきかを判断
- 情報が不足している場合は無理に含めない
- 5-8枚程度の適切な長さに調整

抽出された情報:
{extractedInfo}

【出力形式】
選択したスライドを order 順に並べて、以下のJSON配列で出力してください：

[
  {
    "slideType": "cover",
    "component": "CoverSlide",
    "props": {
      "companyName": "会社名",
      "title": "プレゼンテーションタイトル"
    },
    "order": 1
  }
]

必ず component フィールドには正確なコンポーネント名を使用してください。
props は空のオブジェクト {} でも構いません（後でGenerateSlidesAgentが詳細を生成します）。
`;

export class PlanSlidesAgent {
  private model: ChatOpenAI;

  constructor() {
    this.model = new ChatOpenAI({
      modelName: 'gpt-4o',  // より高性能なモデルに変更
      temperature: 0.1,     // 出力の安定性を向上
      timeout: 60000        // タイムアウトを設定
    });
  }

  async execute(input: PlanSlidesInput): Promise<PlanSlidesOutput> {
    try {
      console.log('PlanSlidesAgent: スライド構成の計画を開始します');
      
      // テンプレート情報を文字列化
      const coreSlides = CORE_SLIDES.map(slide => 
        `- ${slide.component}: ${slide.description}`
      ).join('\n');
      
      const optionalSlides = OPTIONAL_SLIDES.map(slide => 
        `- ${slide.component}: ${slide.description}\n  条件: ${slide.conditions?.join(', ') || 'なし'}`
      ).join('\n\n');

      const prompt = PLAN_SLIDES_PROMPT
        .replace('{coreSlides}', coreSlides)
        .replace('{optionalSlides}', optionalSlides)
        .replace('{extractedInfo}', JSON.stringify(input.extractedInfo, null, 2));

      const response = await this.model.invoke([
        {
          role: 'system',
          content: prompt
        },
        {
          role: 'user',
          content: 'プレゼンテーションに最適なスライド構成を選択し、JSON配列で出力してください。'
        }
      ]);

      console.log('PlanSlidesAgent: AIからの応答を受信しました');
      
      // レスポンスからJSONを抽出
      const content = response.content as string;
      let slideStructure;
      
      try {
        // 複数パターンでJSON抽出を試みる
        let jsonStr = '';
        
        // パターン1: ```json ... ``` 
        const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          jsonStr = jsonMatch[1];
        } 
        // パターン2: 全体がJSONの場合（直接パース）
        else if (content.trim().startsWith('[')) {
          jsonStr = content.trim();
        }
        // パターン3: [ ... ] の最初のJSON配列
        else {
          const startIndex = content.indexOf('[');
          if (startIndex !== -1) {
            let bracketCount = 0;
            let endIndex = -1;
            
            for (let i = startIndex; i < content.length; i++) {
              if (content[i] === '[') {
                bracketCount++;
              } else if (content[i] === ']') {
                bracketCount--;
                if (bracketCount === 0) {
                  endIndex = i;
                  break;
                }
              }
            }
            
            if (endIndex !== -1) {
              jsonStr = content.substring(startIndex, endIndex + 1);
            }
          }
        }
        
        if (jsonStr) {
          slideStructure = JSON.parse(jsonStr.trim());
          console.log('PlanSlidesAgent: JSONの解析に成功しました');
        } else {
          throw new Error('JSON not found in response');
        }
      } catch (parseError) {
        console.error('PlanSlidesAgent: JSONの解析に失敗しました:', parseError);
        console.error('PlanSlidesAgent: 元の応答:', content);
        
        // フォールバック: 基本的なスライド構成
        console.log('PlanSlidesAgent: フォールバック構成を使用します');
        slideStructure = [
          {
            slideType: "cover",
            component: "CoverSlide",
            props: {},
            order: 1
          },
          {
            slideType: "summary",
            component: "ExecutiveSummarySlide", 
            props: {},
            order: 2
          },
          {
            slideType: "problem",
            component: "ProblemAnalysisSlide",
            props: {},
            order: 3
          },
          {
            slideType: "solution",
            component: "SolutionOverviewSlide",
            props: {},
            order: 4
          },
          {
            slideType: "next_steps",
            component: "NextStepsSlide",
            props: {},
            order: 5
          }
        ];
      }
      
      // スライド構成の検証と補正
      slideStructure = this.validateAndCorrectStructure(slideStructure);
      
      // Zodでバリデーション
      console.log('PlanSlidesAgent: データのバリデーションを開始します');
      const validatedStructure = slideStructureSchema.parse(slideStructure);
      console.log('PlanSlidesAgent: データのバリデーションに成功しました');
      
      return {
        slideStructure: validatedStructure.map(slide => ({
          ...slide,
          props: slide.props ?? {}
        }))
      };
    } catch (error) {
      console.error('PlanSlidesAgent: エラーが発生しました:', error);
      throw error;
    }
  }

  /**
   * スライド構成を検証し、必要に応じて補正する
   */
  private validateAndCorrectStructure(structure: any[]): any[] {
    // 必須スライドが含まれているかチェック
    const coreComponents = CORE_SLIDES.map(slide => slide.component);
    const existingComponents = structure.map(slide => slide.component);
    
    // 不足している必須スライドを追加
    const missingCoreSlides = coreComponents.filter(component => 
      !existingComponents.includes(component)
    );
    
    for (const missingComponent of missingCoreSlides) {
      const template = CORE_SLIDES.find(slide => slide.component === missingComponent);
      if (template) {
        console.log(`PlanSlidesAgent: 必須スライド ${missingComponent} を追加します`);
        structure.push({
          slideType: template.slideType,
          component: template.component,
          props: {},
          order: template.order
        });
      }
    }
    
    // orderでソート
    structure.sort((a, b) => a.order - b.order);
    
    // 存在しないコンポーネントを除外
    const validComponents = ALL_SLIDE_TEMPLATES.map(t => t.component);
    const validStructure = structure.filter(slide => {
      if (!validComponents.includes(slide.component)) {
        console.warn(`PlanSlidesAgent: 不明なコンポーネント ${slide.component} を除外します`);
        return false;
      }
      return true;
    });
    
    console.log(`PlanSlidesAgent: 最終的なスライド構成: ${validStructure.map(s => s.component).join(', ')}`);
    
    return validStructure;
  }
}
