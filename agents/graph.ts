import { StateGraph, START, END, Annotation } from '@langchain/langgraph';
import { AgentState } from './types';
import { AnalyzeNotesAgent } from './nodes/analyzeNotes';
import { PlanSlidesAgent } from './nodes/planSlides';
import { GenerateSlidesAgent } from './nodes/generateSlides';
import { USE_MOCK_MODE } from './config';
import { mockAgentResult } from './mock';

// StateAnnotationの定義
const StateAnnotation = Annotation.Root({
  meetingNotes: Annotation<string>,
  extractedInfo: Annotation<any>,
  slideStructure: Annotation<any[]>,
  generatedSlides: Annotation<any[]>,
  errors: Annotation<string[]>({ 
    reducer: (existing, update) => [...(existing || []), ...(update || [])]
  })
});

// エージェントのインスタンスを作成（モックモードでない場合のみ）
const analyzeNotesAgent = !USE_MOCK_MODE ? new AnalyzeNotesAgent() : null;
const planSlidesAgent = !USE_MOCK_MODE ? new PlanSlidesAgent() : null;
const generateSlidesAgent = !USE_MOCK_MODE ? new GenerateSlidesAgent() : null;

// ノード関数の定義
async function analyzeNotes(state: typeof StateAnnotation.State) {
  if (!analyzeNotesAgent) {
    throw new Error('AnalyzeNotesAgent is not initialized');
  }
  
  try {
    const result = await analyzeNotesAgent.execute({
      meetingNotes: state.meetingNotes
    });
    
    return {
      extractedInfo: result.extractedInfo
    };
  } catch (error) {
    return {
      errors: [`議事録分析エラー: ${error}`]
    };
  }
}

async function planSlides(state: typeof StateAnnotation.State) {
  if (!planSlidesAgent) {
    throw new Error('PlanSlidesAgent is not initialized');
  }
  
  try {
    const result = await planSlidesAgent.execute({
      extractedInfo: state.extractedInfo
    });
    
    return {
      slideStructure: result.slideStructure
    };
  } catch (error) {
    return {
      errors: [`スライド構成計画エラー: ${error}`]
    };
  }
}

async function generateSlides(state: typeof StateAnnotation.State) {
  if (!generateSlidesAgent) {
    throw new Error('GenerateSlidesAgent is not initialized');
  }
  
  try {
    const result = await generateSlidesAgent.execute({
      extractedInfo: state.extractedInfo,
      slideStructure: state.slideStructure
    });
    
    return {
      generatedSlides: result.generatedSlides
    };
  } catch (error) {
    return {
      errors: [`スライド生成エラー: ${error}`]
    };
  }
}

// グラフの構築
export function createSlideGeneratorGraph() {
  const workflow = new StateGraph(StateAnnotation);

  // ノードの追加
  workflow.addNode('analyze_notes', analyzeNotes);
  workflow.addNode('plan_slides', planSlides);
  workflow.addNode('generate_slides', generateSlides);

  // エッジの追加（順次実行）
  // 型エラーを回避するために一時的にasを使用
  (workflow.addEdge as any)(START, 'analyze_notes');
  (workflow.addEdge as any)('analyze_notes', 'plan_slides');
  (workflow.addEdge as any)('plan_slides', 'generate_slides');
  (workflow.addEdge as any)('generate_slides', END);

  return workflow.compile();
}

// 議事録からスライドを生成する関数
export async function generateSlidesFromMeetingNotes(meetingNotes: string): Promise<AgentState> {
  // モックモードの場合はモックデータを返す
  if (USE_MOCK_MODE) {
    console.log('Using mock mode for slide generation');
    return {
      ...mockAgentResult,
      meetingNotes
    };
  }

  const app = createSlideGeneratorGraph();
  
  const initialState = {
    meetingNotes,
    extractedInfo: {},
    slideStructure: [],
    generatedSlides: [],
    errors: []
  };

  const result = await app.invoke(initialState);
  
  // 結果をAgentState形式に変換して返す
  return {
    meetingNotes: result.meetingNotes,
    extractedInfo: result.extractedInfo,
    slideStructure: result.slideStructure,
    generatedSlides: result.generatedSlides,
    errors: result.errors
  } as AgentState;
}
