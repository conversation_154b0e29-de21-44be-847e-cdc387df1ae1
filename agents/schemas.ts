import { z } from 'zod';

// 議事録分析結果のスキーマ
export const extractedInfoSchema = z.object({
  companyName: z.string().optional(),
  mainTopic: z.string().optional(),
  currentChallenges: z.array(z.object({
    category: z.string(),
    description: z.string(),
    impact: z.enum(['high', 'medium', 'low'])
  })).optional(),
  idealState: z.array(z.string()).optional(),
  proposedSolutions: z.array(z.object({
    problemCategory: z.string(),
    solution: z.string(),
    benefits: z.array(z.string())
  })).optional(),
  roi: z.object({
    investment: z.array(z.object({
      item: z.string(),
      amount: z.number()
    })),
    returns: z.array(z.object({
      item: z.string(),
      amount: z.number(),
      timeframe: z.string().optional()
    })),
    percentage: z.number()
  }).optional(),
  timeline: z.array(z.object({
    date: z.string(),
    title: z.string(),
    description: z.string().optional()
  })).optional(),
  nextSteps: z.array(z.object({
    title: z.string(),
    description: z.string(),
    deadline: z.string().optional(),
    priority: z.enum(['high', 'medium', 'low']).optional()
  })).optional()
});

// スライド構成のスキーマ
export const slideStructureSchema = z.array(z.object({
  slideType: z.string(),
  component: z.string(),
  props: z.any(),
  order: z.number()
}));

// 生成されたスライドのスキーマ
export const generatedSlidesSchema = z.array(z.object({
  component: z.string(),
  props: z.any()
}));
