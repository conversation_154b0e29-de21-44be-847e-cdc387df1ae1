// エージェントの状態を定義
export interface AgentState {
  // 入力された議事録
  meetingNotes: string;
  
  // 抽出された情報
  extractedInfo: {
    companyName?: string;
    mainTopic?: string;
    currentChallenges?: Array<{
      category: string;
      description: string;
      impact: 'high' | 'medium' | 'low';
    }>;
    idealState?: string[];
    proposedSolutions?: Array<{
      problemCategory: string;
      solution: string;
      benefits: string[];
    }>;
    roi?: {
      investment: Array<{ item: string; amount: number }>;
      returns: Array<{ item: string; amount: number; timeframe?: string }>;
      percentage: number;
    };
    timeline?: Array<{
      date: string;
      title: string;
      description?: string;
    }>;
    nextSteps?: Array<{
      title: string;
      description: string;
      deadline?: string;
      priority?: 'high' | 'medium' | 'low';
    }>;
  };
  
  // スライド構成案
  slideStructure: Array<{
    slideType: string;
    component: string;
    props: any;
    order: number;
  }>;
  
  // 生成されたスライドデータ
  generatedSlides: Array<{
    component: string;
    props: any;
  }>;
  
  // エラー情報
  errors: string[];
}

// 各エージェントの入出力型定義
export interface AnalyzeNotesInput {
  meetingNotes: string;
}

export interface AnalyzeNotesOutput {
  extractedInfo: AgentState['extractedInfo'];
}

export interface PlanSlidesInput {
  extractedInfo: AgentState['extractedInfo'];
}

export interface PlanSlidesOutput {
  slideStructure: AgentState['slideStructure'];
}

export interface GenerateSlidesInput {
  extractedInfo: AgentState['extractedInfo'];
  slideStructure: AgentState['slideStructure'];
}

export interface GenerateSlidesOutput {
  generatedSlides: AgentState['generatedSlides'];
}

// スライドコンポーネントマッピング
export const SLIDE_COMPONENTS = {
  COVER: 'CoverSlide',
  EXECUTIVE_SUMMARY: 'ExecutiveSummarySlide',
  PROBLEM_ANALYSIS: 'ProblemAnalysisSlide',
  AS_IS: 'AsIsSlide',
  SOLUTION_OVERVIEW: 'SolutionOverviewSlide',
  ROI_CALCULATION: 'ROICalculationSlide',
  TIMELINE: 'TimelineSlide',
  COMPARISON_TABLE: 'ComparisonTableSlide',
  METRICS: 'MetricsSlide',
  NEXT_STEPS: 'NextStepsSlide',
  CASE_INFO: 'CaseInfoSlide',
  VENN_DIAGRAM: 'VennDiagram',
  CYCLE_DIAGRAM: 'AutoCycleDiagram',
  PROCESS_FLOW: 'ProcessFlowSlide',
  TEAM_STRUCTURE: 'TeamStructureSlide',
  RISK_MATRIX: 'RiskMatrixSlide',
  BUDGET_BREAKDOWN: 'BudgetBreakdownSlide'
} as const;
