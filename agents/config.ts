// 環境変数の設定を確認するためのヘルパー
export function checkEnvironmentVariables() {
  const requiredEnvVars = ['OPENAI_API_KEY'];
  const missingEnvVars = requiredEnvVars.filter(
    (envVar) => !process.env[envVar]
  );

  if (missingEnvVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingEnvVars.join(', ')}. ` +
      'Please create a .env.local file with these variables.'
    );
  }
}

// モックモードで動作するかどうか
export const USE_MOCK_MODE = process.env.NEXT_PUBLIC_USE_MOCK_MODE === 'true';
