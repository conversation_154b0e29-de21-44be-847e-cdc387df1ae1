/* Atomic type definitions */
export interface HasLabel { label?: string }
export interface HasKeyMessage { keyMessage?: string }
export interface HasMainTitle { mainTitle?: string }
export interface HasMainContent { mainContent?: string }

/* Commonly used composite types */
export type SlideContent =
  HasLabel &
  HasKeyMessage &
  HasMainTitle &
  HasMainContent;

export interface AsIsSlideProps
  extends HasLabel,
    HasKeyMessage {
  categories?: string[]
  asIs?: string[][]
  toBe?: string[][]
  asIsTitle?: string
  toBeTitle?: string
  footnote?: string
}

/** Cover slide properties */
export interface CoverSlideProps {
  /** Company name (displayed with "御中" suffix) */
  companyName?: string
  /** Main title */
  mainTitle?: string
  /** Subtitle */
  subTitle?: string
  /** Date (free format) */
  date?: string
  /** Logo source (optional) */
  logoSrc?: string
}