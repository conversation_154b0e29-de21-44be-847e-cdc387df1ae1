import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Message } from './props';

// エグゼクティブサマリースライドのpropsを定義
export interface ExecutiveSummarySlideProps extends HasLabel, HasKeyMessage {
  sections?: {
    title: string;
    points?: string[];
    icon?: string; // optional icon class
  }[];
  highlightColor?: string;
}

// 課題分析スライドのprops
export interface ProblemAnalysisSlideProps extends HasLabel, HasKeyMessage {
  problems?: {
    category: string;
    description: string;
    impact: 'high' | 'medium' | 'low';
    details?: string[];
  }[];
  futureProjection?: {
    title: string;
    description: string;
  };
}

// ソリューション概要スライドのprops
export interface SolutionOverviewSlideProps extends HasLabel, HasKeyMessage {
  solutions?: {
    problemCategory: string;
    solution: string;
    benefits: string[];
  }[];
  centralConcept?: string;
}

// ROI計算スライドのprops
export interface ROICalculationSlideProps extends HasLabel, HasKeyMessage {
  investment?: {
    item: string;
    amount: number;
    unit?: string;
  }[];
  returns?: {
    item: string;
    amount: number;
    unit?: string;
    timeframe?: string;
  }[];
  totalROI?: {
    percentage: number;
    paybackPeriod?: string;
  };
  notes?: string[];
}

// タイムラインスライドのprops
export interface TimelineSlideProps extends HasLabel, HasKeyMessage {
  milestones: {
    date: string;
    title: string;
    description?: string;
    status?: 'completed' | 'in-progress' | 'planned';
  }[];
  orientation?: 'horizontal' | 'vertical';
}

// 比較表スライドのprops
export interface ComparisonTableSlideProps extends HasLabel, HasKeyMessage {
  headers: string[];
  rows: {
    label: string;
    values: (string | { text: string; highlight?: boolean })[];
  }[];
  highlightColumn?: number;
}

// 指標・KPIスライドのprops
export interface MetricsSlideProps extends HasLabel, HasKeyMessage {
  metrics: {
    label: string;
    value: string | number;
    unit?: string;
    trend?: 'up' | 'down' | 'stable';
    target?: string | number;
    description?: string;
  }[];
  layout?: 'grid' | 'list';
}

// プロセスフロー図のprops
export interface ProcessFlowSlideProps extends HasLabel, HasKeyMessage {
  steps: {
    id: string;
    label: string;
    description?: string;
    type?: 'start' | 'process' | 'decision' | 'end';
  }[];
  connections: {
    from: string;
    to: string;
    label?: string;
  }[];
}

// チーム構成図のprops
export interface TeamStructureSlideProps extends HasLabel, HasKeyMessage {
  teams: {
    name: string;
    role: string;
    members?: string[];
    responsibilities?: string[];
    level?: number; // hierarchy level
  }[];
  showHierarchy?: boolean;
}

// リスクマトリックスのprops
export interface RiskMatrixSlideProps extends HasLabel, HasKeyMessage {
  risks: {
    name: string;
    probability: 'low' | 'medium' | 'high';
    impact: 'low' | 'medium' | 'high';
    mitigation?: string;
  }[];
  showMitigation?: boolean;
}

// 予算内訳のprops
export interface BudgetBreakdownSlideProps extends HasLabel, HasKeyMessage {
  categories: {
    name: string;
    amount: number;
    percentage?: number;
    subcategories?: {
      name: string;
      amount: number;
    }[];
  }[];
  total: number;
  currency?: string;
  showChart?: boolean;
}

// 次のステップスライドのprops
export interface NextStepsSlideProps extends HasLabel, HasKeyMessage {
  steps: {
    title: string;
    description: string;
    deadline?: string;
    responsible?: string;
    priority?: 'high' | 'medium' | 'low';
  }[];
  callToAction?: string;
}
