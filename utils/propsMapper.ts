/**
 * AIが生成したpropsを各スライドコンポーネントが期待する形式に変換する
 */

export function mapPropsForComponent(componentName: string, aiGeneratedProps: any): any {
  // 共通のデフォルト値
  const baseProps = {
    label: aiGeneratedProps.label || '',
    keyMessage: aiGeneratedProps.keyMessage || '',
    ...aiGeneratedProps
  };

  switch (componentName) {
    case 'CoverSlide':
      return {
        ...baseProps,
        companyName: aiGeneratedProps.companyName || '',
        mainTitle: aiGeneratedProps.mainTitle || aiGeneratedProps.title || 'タイトル未設定',
        subTitle: aiGeneratedProps.subTitle || aiGeneratedProps.subtitle || '',
        date: aiGeneratedProps.date || '',
        logoSrc: aiGeneratedProps.logoSrc
      };

    case 'ExecutiveSummarySlide':
      return {
        ...baseProps,
        sections: aiGeneratedProps.sections || []
      };

    case 'ProblemAnalysisSlide':
      return {
        ...baseProps,
        // AIが生成する可能性のあるフィールド名を適切にマッピング
        problems: aiGeneratedProps.problems || 
          (aiGeneratedProps.currentChallenges?.map((challenge: any) => ({
            category: challenge.category || 'カテゴリ未定義',
            description: challenge.description || '',
            impact: challenge.impact || 'medium',
            details: challenge.details || []
          })) || []),
        futureProjection: aiGeneratedProps.futureProjection || 
          (aiGeneratedProps.idealState?.length > 0 ? {
            title: '理想的な状態',
            description: aiGeneratedProps.idealState.join('、')
          } : undefined)
      };

    case 'AsIsSlide':
      // AIが生成したデータを適切に変換
      let categories = aiGeneratedProps.categories || [];
      let asIs = aiGeneratedProps.asIs || [];
      let toBe = aiGeneratedProps.toBe || [];

      // currentChallengesからcategoriesとasIsを生成
      if (aiGeneratedProps.currentChallenges?.length > 0) {
        categories = aiGeneratedProps.currentChallenges.map((challenge: any) => 
          challenge.category || 'カテゴリ未定義'
        );
        asIs = aiGeneratedProps.currentChallenges.map((challenge: any) => 
          [challenge.description || '']
        );
      }

      // idealStateからtoBeを生成
      if (aiGeneratedProps.idealState?.length > 0) {
        if (categories.length > 0) {
          const itemsPerCategory = Math.ceil(aiGeneratedProps.idealState.length / categories.length);
          toBe = categories.map((_: any, idx: number) => {
            const start = idx * itemsPerCategory;
            const end = start + itemsPerCategory;
            return aiGeneratedProps.idealState.slice(start, end);
          });
        } else {
          categories = ['改善項目'];
          toBe = [aiGeneratedProps.idealState];
          if (asIs.length === 0) {
            asIs = [['現状の課題']];
          }
        }
      }

      // proposedSolutionsからデータを生成
      if (aiGeneratedProps.proposedSolutions?.length > 0 && categories.length === 0) {
        categories = aiGeneratedProps.proposedSolutions.map((solution: any) => 
          solution.problemCategory || 'カテゴリ未定義'
        );
        asIs = aiGeneratedProps.proposedSolutions.map((solution: any) => 
          [solution.problemCategory ? '課題が存在' : '']
        );
        toBe = aiGeneratedProps.proposedSolutions.map((solution: any) => [
          solution.solution || '',
          ...(solution.benefits || [])
        ]);
      }

      return {
        ...baseProps,
        categories,
        asIs,
        toBe
      };

    case 'ROICalculationSlide':
      return {
        ...baseProps,
        investment: aiGeneratedProps.investment || [],
        returns: aiGeneratedProps.returns || [],
        totalROI: aiGeneratedProps.totalROI || { percentage: 0 },
        notes: aiGeneratedProps.notes || []
      };

    case 'SolutionOverviewSlide':
      return {
        ...baseProps,
        solutions: aiGeneratedProps.solutions || 
          (aiGeneratedProps.proposedSolutions?.map((solution: any) => ({
            problemCategory: solution.problemCategory || '',
            solution: solution.solution || '',
            benefits: Array.isArray(solution.benefits) ? solution.benefits : []
          })) || []),
        centralConcept: aiGeneratedProps.centralConcept
      };

    case 'NextStepsSlide':
      return {
        ...baseProps,
        steps: aiGeneratedProps.steps || 
          (aiGeneratedProps.nextSteps?.map((step: any) => ({
            title: step.title || '',
            description: step.description || '',
            deadline: step.deadline,
            responsible: step.responsible,
            priority: step.priority || 'medium'
          })) || []),
        callToAction: aiGeneratedProps.callToAction
      };

    case 'TimelineSlide':
      // timelineEventsとnextStepsを結合してmilestonesを作成
      let milestones = aiGeneratedProps.milestones || [];
      
      if (milestones.length === 0) {
        const timelineItems = (aiGeneratedProps.timelineEvents || []).map((item: any) => ({
          date: item.date || '',
          title: item.title || '',
          description: item.description,
          status: item.status || 'completed'
        }));
        
        const nextStepItems = (aiGeneratedProps.nextSteps || []).map((step: any) => ({
          date: step.deadline || '予定',
          title: step.title || '',
          description: step.description || '',
          status: 'planned' as const
        }));
        
        milestones = [...timelineItems, ...nextStepItems];
      }
      
      return {
        ...baseProps,
        milestones,
        orientation: aiGeneratedProps.orientation || 'horizontal'
      };

    default:
      // 未知のコンポーネントの場合は、そのまま返す
      return baseProps;
  }
}
