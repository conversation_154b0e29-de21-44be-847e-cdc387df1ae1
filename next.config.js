/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  typescript: {
    // 型エラーを無視してビルドを継続
    ignoreBuildErrors: true,
  },
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        async_hooks: false,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        stream: false,
        http: false,
        https: false,
        zlib: false,
        path: false,
        os: false,
        util: false,
        buffer: false,
        process: false,
        module: false,
        events: false,
        assert: false,
        constants: false,
        child_process: false,
        dns: false,
        dgram: false,
        readline: false,
        repl: false,
        string_decoder: false,
        timers: false,
        tty: false,
        url: false,
        vm: false,
      };
    }
    return config;
  },
};

module.exports = nextConfig;