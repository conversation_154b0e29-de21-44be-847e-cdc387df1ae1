import { NextResponse } from 'next/server';
import { generateSlidesFromMeetingNotes } from '@/agents/graph';

export async function POST(request: Request) {
  try {
    const { meetingNotes } = await request.json();
    
    if (!meetingNotes) {
      return NextResponse.json(
        { error: '議事録が提供されていません' },
        { status: 400 }
      );
    }

    const result = await generateSlidesFromMeetingNotes(meetingNotes);
    return NextResponse.json(result);
  } catch (error) {
    console.error('スライド生成エラー:', error);
    return NextResponse.json(
      { error: 'スライド生成中にエラーが発生しました' },
      { status: 500 }
    );
  }
} 