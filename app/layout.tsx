import './globals.css';
import StagewiseProvider from './stagewise-provider';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Spectacle Presentation',
  description: 'Created with Spectacle, Tailwind CSS, and shadcn/ui',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="ja">
      <body className="font-sans">
        <StagewiseProvider>
          {children}
        </StagewiseProvider>
      </body>
    </html>
  );
}