import React, { useEffect } from 'react';
import { AsIsSlideProps } from '@/types/props';
import SlideLabel from './SlideParts/SlideLabel';
import SlideKeyMessage from './SlideParts/SlideKeyMessage';

export default function AsIsSlide({
  label = 'As-Is vs To-Be',
  keyMessage = '',
  categories = [],
  asIs = [],
  toBe = [],
  asIsTitle = '現状（As-is）',
  toBeTitle = 'あるべき姿（To-be）',
  footnote,
  // AIが生成する可能性のあるフィールド名に対応
  currentChallenges,
  idealState,
  proposedSolutions,
  ...otherProps
}: AsIsSlideProps & { 
  currentChallenges?: any[]; 
  idealState?: string[]; 
  proposedSolutions?: any[];
  [key: string]: any;
}) {
  // デバッグ用：propsの内容をログ出力
  useEffect(() => {
    console.log('AsIsSlide props:', {
      label,
      keyMessage,
      categories,
      asIs,
      toBe,
      currentChallenges,
      idealState,
      proposedSolutions,
      otherProps
    });
  }, [label, keyMessage, categories, asIs, toBe, currentChallenges, idealState, proposedSolutions, otherProps]);

  // AIが生成したデータ構造を適切に変換
  let actualCategories = categories;
  let actualAsIs = asIs;
  let actualToBe = toBe;

  // AIが生成したデータから変換
  if (currentChallenges && currentChallenges.length > 0) {
    actualCategories = currentChallenges.map(challenge => challenge.category || 'カテゴリ未定義');
    actualAsIs = currentChallenges.map(challenge => [challenge.description || '']);
  }

  if (idealState && idealState.length > 0) {
    // idealStateを各カテゴリに対応する形に分割
    if (actualCategories.length > 0) {
      const itemsPerCategory = Math.ceil(idealState.length / actualCategories.length);
      actualToBe = actualCategories.map((_, idx) => {
        const start = idx * itemsPerCategory;
        const end = start + itemsPerCategory;
        return idealState.slice(start, end);
      });
    } else {
      // カテゴリがない場合は全てを1つのカテゴリとして扱う
      actualCategories = ['改善項目'];
      actualToBe = [idealState];
      if (actualAsIs.length === 0) {
        actualAsIs = [['現状の課題']];
      }
    }
  }

  // proposedSolutionsから追加のTo-beデータを生成
  if (proposedSolutions && proposedSolutions.length > 0) {
    if (actualCategories.length === 0) {
      actualCategories = proposedSolutions.map(solution => solution.problemCategory || 'カテゴリ未定義');
      actualAsIs = proposedSolutions.map(solution => [solution.problemCategory ? '課題が存在' : '']);
      actualToBe = proposedSolutions.map(solution => [
        solution.solution || '',
        ...(solution.benefits || [])
      ]);
    }
  }

  // データが不十分な場合のフォールバック表示
  if (!actualCategories || actualCategories.length === 0) {
    return (
      <div className="absolute inset-0 w-full h-full bg-white font-sans">
        <SlideLabel label={label} />
        <SlideKeyMessage keyMessage={keyMessage} />
        <div className="pt-40 px-8 w-full">
          <div className="flex items-center justify-center h-64">
            <p className="text-gray-500 text-lg">比較データがありません</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="absolute inset-0 w-full h-full bg-white font-sans">
      {/* Label & Key Message */}
      <SlideLabel label={label} />
      <SlideKeyMessage keyMessage={keyMessage} />

      {/* Content */}
      <div className="pt-40 px-6 w-full flex justify-center">
        <div className="w-full max-w-6xl grid gap-6">
          {/* Header row */}
          <div className="grid grid-cols-[1fr_4fr_4fr] gap-6">
            <div></div>
            <div className="bg-gray-600 text-white rounded-md py-2 text-center font-bold">
              {asIsTitle}
            </div>
            <div className="bg-primary text-white rounded-md py-2 text-center font-bold">
              {toBeTitle}
            </div>
          </div>

          {/* Category rows */}
          {actualCategories.map((cat, idx) => (
            <div
              key={idx}
              className="grid grid-cols-[1fr_4fr_4fr] gap-6"
            >
              {/* Category card */}
              <div className="bg-gray-500 text-white rounded-md flex items-center justify-center p-4 font-bold text-sm">
                {cat}
              </div>

              {/* As-is card */}
              <div className="bg-gray-100 rounded-md shadow-sm p-4">
                <ul className="list-disc list-inside space-y-1 marker:text-gray-600 text-sm leading-relaxed">
                  {(actualAsIs[idx] || ['データなし']).map((item, i) => (
                    <li key={i}>{item}</li>
                  ))}
                </ul>
              </div>

              {/* To-be card */}
              <div className="relative bg-blue-50 rounded-md shadow-sm p-4">
                <span
                  className="absolute -left-5 top-1/2 -translate-y-1/2
                         text-primary text-xl leading-none select-none"
                >
                  ▶︎
                </span>
                <ul className="list-disc list-inside space-y-1 marker:text-gray-600 text-sm leading-relaxed">
                  {(actualToBe[idx] || ['データなし']).map((item, i) => (
                    <li key={i}>{item}</li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Footer note */}
      {footnote && (
        <p className="absolute inset-x-0 bottom-4 text-center text-xs text-gray-500">
          ※{footnote}
        </p>
      )}
    </div>
  );
}