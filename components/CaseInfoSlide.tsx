import React from 'react';
import { SlideContent } from '@/types/props';
import SlideLabel from './SlideParts/SlideLabel';
import SlideKeyMessage from './SlideParts/SlideKeyMessage';
import SlideBody from './SlideParts/SlideBody';

export default function CaseInfoSlide({
  label,
  keyMessage,
  mainTitle,
  mainContent
}: SlideContent) {
  return (
    <div className="relative w-full h-full">
      <SlideLabel label={label} />
      <SlideKeyMessage keyMessage={keyMessage} />
      <SlideBody
        mainTitle={mainTitle}
        mainContent={mainContent}
      />
    </div>
  );
}