import React from 'react';
import { NextStepsSlideProps } from '@/types/slide-props';
import SlideLabel from './SlideParts/SlideLabel';
import SlideKeyMessage from './SlideParts/SlideKeyMessage';

export default function NextStepsSlide({
  label,
  keyMessage,
  steps,
  callToAction
}: NextStepsSlideProps) {
  const getPriorityColor = (priority?: 'high' | 'medium' | 'low') => {
    switch (priority) {
      case 'high': return 'bg-red-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const getPriorityText = (priority?: 'high' | 'medium' | 'low') => {
    switch (priority) {
      case 'high': return '高';
      case 'medium': return '中';
      case 'low': return '低';
      default: return '';
    }
  };

  return (
    <div className="absolute inset-0 w-full h-full bg-white font-sans">
      <SlideLabel label={label} />
      <SlideKeyMessage keyMessage={keyMessage} />

      <div className="pt-40 px-8 w-full">
        <div className="max-w-6xl mx-auto">
          <div className="space-y-4">
            {steps?.map((step, idx) => (
              <div
                key={idx}
                className="bg-white border-2 border-gray-200 rounded-lg p-6 hover:border-blue-300 transition-colors"
              >
                <div className="flex items-start">
                  {/* Step Number */}
                  <div className="flex-shrink-0 w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-lg mr-4">
                    {idx + 1}
                  </div>
                  
                  {/* Content */}
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="text-lg font-bold text-gray-800">
                        {step.title}
                      </h3>
                      {step.priority && (
                        <span className={`${getPriorityColor(step.priority)} text-white text-xs px-2 py-1 rounded-full font-medium ml-3`}>
                          優先度: {getPriorityText(step.priority)}
                        </span>
                      )}
                    </div>
                    
                    <p className="text-gray-700 mb-3 leading-relaxed">
                      {step.description}
                    </p>
                    
                    <div className="flex items-center gap-6 text-sm text-gray-600">
                      {step.deadline && (
                        <div className="flex items-center">
                          <span className="font-medium mr-1">期限:</span>
                          <span>{step.deadline}</span>
                        </div>
                      )}
                      {step.responsible && (
                        <div className="flex items-center">
                          <span className="font-medium mr-1">担当:</span>
                          <span>{step.responsible}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Call to Action */}
          {callToAction && (
            <div className="mt-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white text-center">
              <p className="text-xl font-bold">
                {callToAction}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
