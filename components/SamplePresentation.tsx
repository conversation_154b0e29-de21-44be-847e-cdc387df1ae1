'use client';

import React from 'react';
import {
  Deck,
  Slide,
  FlexBox
} from 'spectacle';

import CoverSlide from '@/components/CoverSlide';
import ExecutiveSummarySlide from '@/components/ExecutiveSummarySlide';
import ProblemAnalysisSlide from '@/components/ProblemAnalysisSlide';
import AsIsSlide from '@/components/AsIsSlide';
import SolutionOverviewSlide from '@/components/SolutionOverviewSlide';
import ROICalculationSlide from '@/components/ROICalculationSlide';
import TimelineSlide from '@/components/TimelineSlide';
import ComparisonTableSlide from '@/components/ComparisonTableSlide';
import MetricsSlide from '@/components/MetricsSlide';
import NextStepsSlide from '@/components/NextStepsSlide';
import CaseInfoSlide from '@/components/CaseInfoSlide';
import VennDiagram from '@/components/VennDiagram';
import AutoCycleDiagram from '@/components/AutoCycleDiagram';

const theme = {
  colors: {
    primary: '#143d8f',
    secondary: '#003886',
  },
  fonts: {
    header: '"Noto Sans JP", sans-serif',
    text: '"Noto Sans JP", sans-serif',
  },
};

export default function SamplePresentation() {
  return (
    <div style={{ height: 'calc(100vh - 64px)', overflow: 'hidden' }}>
      <Deck theme={theme}>
        {/* Slide 1: Cover */}
        <Slide backgroundColor="white">
          <FlexBox height="100%" width="100%" justifyContent="center" alignItems="center">
            <CoverSlide
              companyName="株式会社テクノロジー"
              mainTitle="DXプロジェクト提案書"
              subTitle="業務効率化とイノベーション創出に向けて"
              date="2025年5月24日"
            />
          </FlexBox>
        </Slide>

        {/* Slide 2: Executive Summary */}
        <Slide backgroundColor="white">
          <FlexBox height="100%" width="100%" justifyContent="center" alignItems="center">
            <ExecutiveSummarySlide
              label="エグゼクティブサマリー"
              keyMessage="DXで実現する持続的成長"
              sections={[
                {
                  title: "現状の課題",
                  points: [
                    "手作業による業務効率の低下",
                    "データの分散による意思決定の遅延",
                    "競合他社のデジタル化進展"
                  ],
                  icon: "⚠️"
                },
                {
                  title: "提案ソリューション",
                  points: [
                    "業務自動化システムの導入",
                    "統合データ基盤の構築",
                    "AIを活用した予測分析"
                  ],
                  icon: "💡"
                },
                {
                  title: "期待される効果",
                  points: [
                    "業務効率30%向上",
                    "意思決定スピード2倍",
                    "年間1.5億円のコスト削減"
                  ],
                  icon: "📈"
                }
              ]}
            />
          </FlexBox>
        </Slide>

        {/* Slide 3: Problem Analysis */}
        <Slide backgroundColor="white">
          <FlexBox height="100%" width="100%" justifyContent="center" alignItems="center">
            <ProblemAnalysisSlide
              label="現状分析"
              keyMessage="解決すべき3つの重要課題"
              problems={[
                {
                  category: "業務効率",
                  description: "手作業による定型業務が全体の60%を占め、付加価値の高い業務に時間を割けない",
                  impact: "high",
                  details: ["月間200時間の残業発生", "ヒューマンエラー率5%"]
                },
                {
                  category: "データ管理",
                  description: "部門ごとに異なるシステムを使用し、データの一元管理ができていない",
                  impact: "high",
                  details: ["リアルタイムな経営判断が困難", "データ整合性の問題"]
                },
                {
                  category: "イノベーション",
                  description: "新規事業開発のためのリソースと仕組みが不足",
                  impact: "medium",
                  details: ["競合他社との差別化が困難", "市場機会の逸失"]
                }
              ]}
              futureProjection={{
                title: "このまま放置した場合のリスク",
                description: "2年後には競合他社との技術格差が決定的となり、市場シェアの20%を失う可能性があります。"
              }}
            />
          </FlexBox>
        </Slide>

        {/* Slide 4: Solution Overview */}
        <Slide backgroundColor="white">
          <FlexBox height="100%" width="100%" justifyContent="center" alignItems="center">
            <SolutionOverviewSlide
              label="ソリューション概要"
              keyMessage="統合的なDXアプローチ"
              solutions={[
                {
                  problemCategory: "業務効率",
                  solution: "RPA導入による自動化",
                  benefits: ["定型業務の80%自動化", "エラー率を1%以下に削減"]
                },
                {
                  problemCategory: "データ管理",
                  solution: "クラウド基盤の構築",
                  benefits: ["リアルタイムデータ分析", "部門間連携の強化"]
                },
                {
                  problemCategory: "イノベーション",
                  solution: "AI/ML プラットフォーム",
                  benefits: ["予測分析による意思決定支援", "新サービス開発の加速"]
                }
              ]}
              centralConcept="DX統合プラットフォーム"
            />
          </FlexBox>
        </Slide>

        {/* Slide 5: ROI Calculation */}
        <Slide backgroundColor="white">
          <FlexBox height="100%" width="100%" justifyContent="center" alignItems="center">
            <ROICalculationSlide
              label="投資対効果"
              keyMessage="18ヶ月での投資回収を実現"
              investment={[
                { item: "システム開発費", amount: 80000000 },
                { item: "クラウド基盤構築", amount: 30000000 },
                { item: "教育・研修費用", amount: 10000000 },
                { item: "コンサルティング", amount: 20000000 }
              ]}
              returns={[
                { item: "人件費削減", amount: 60000000, timeframe: "年間" },
                { item: "業務効率化による収益増", amount: 80000000, timeframe: "年間" },
                { item: "エラー削減によるコスト削減", amount: 10000000, timeframe: "年間" }
              ]}
              totalROI={{
                percentage: 107,
                paybackPeriod: "18ヶ月"
              }}
              notes={[
                "3年間の累計効果で算出",
                "間接的な効果（ブランド価値向上等）は含まず"
              ]}
            />
          </FlexBox>
        </Slide>

        {/* Slide 6: Timeline */}
        <Slide backgroundColor="white">
          <FlexBox height="100%" width="100%" justifyContent="center" alignItems="center">
            <TimelineSlide
              label="実施スケジュール"
              keyMessage="段階的な導入で確実な成果を実現"
              milestones={[
                {
                  date: "2025年6月",
                  title: "プロジェクトキックオフ",
                  description: "要件定義・現状分析",
                  status: "planned"
                },
                {
                  date: "2025年9月",
                  title: "Phase 1: RPA導入",
                  description: "主要業務の自動化開始",
                  status: "planned"
                },
                {
                  date: "2025年12月",
                  title: "Phase 2: データ基盤構築",
                  description: "クラウド移行完了",
                  status: "planned"
                },
                {
                  date: "2026年3月",
                  title: "Phase 3: AI活用開始",
                  description: "予測分析システム稼働",
                  status: "planned"
                }
              ]}
            />
          </FlexBox>
        </Slide>

        {/* Slide 7: Comparison Table */}
        <Slide backgroundColor="white">
          <FlexBox height="100%" width="100%" justifyContent="center" alignItems="center">
            <ComparisonTableSlide
              label="ソリューション比較"
              keyMessage="最適なソリューションの選定"
              headers={["評価項目", "Option A: 段階導入", "Option B: 一括導入", "Option C: 部分導入"]}
              rows={[
                {
                  label: "初期投資",
                  values: ["1.4億円", "2.5億円", "0.5億円"]
                },
                {
                  label: "導入期間",
                  values: ["12ヶ月", "6ヶ月", "3ヶ月"]
                },
                {
                  label: "リスク",
                  values: [{ text: "低", highlight: true }, "高", "中"]
                },
                {
                  label: "効果の大きさ",
                  values: [{ text: "大", highlight: true }, { text: "大", highlight: true }, "小"]
                },
                {
                  label: "拡張性",
                  values: [{ text: "高", highlight: true }, "中", "低"]
                }
              ]}
              highlightColumn={1}
            />
          </FlexBox>
        </Slide>

        {/* Slide 8: Metrics */}
        <Slide backgroundColor="white">
          <FlexBox height="100%" width="100%" justifyContent="center" alignItems="center">
            <MetricsSlide
              label="成功指標"
              keyMessage="明確なKPIで進捗を管理"
              metrics={[
                {
                  label: "業務自動化率",
                  value: 80,
                  unit: "%",
                  target: 80,
                  trend: "up",
                  description: "RPAによる定型業務の自動化率"
                },
                {
                  label: "処理時間削減",
                  value: 65,
                  unit: "%",
                  target: 50,
                  trend: "up",
                  description: "主要業務プロセスの処理時間"
                },
                {
                  label: "データ統合率",
                  value: 100,
                  unit: "%",
                  target: 100,
                  trend: "stable",
                  description: "全部門のデータ統合完了"
                },
                {
                  label: "コスト削減額",
                  value: 1.5,
                  unit: "億円",
                  target: 1.2,
                  trend: "up",
                  description: "年間コスト削減額"
                },
                {
                  label: "従業員満足度",
                  value: 85,
                  unit: "%",
                  target: 80,
                  trend: "up",
                  description: "DX推進に対する満足度"
                },
                {
                  label: "新規事業創出",
                  value: 3,
                  unit: "件",
                  target: 2,
                  trend: "up",
                  description: "AIを活用した新サービス"
                }
              ]}
            />
          </FlexBox>
        </Slide>

        {/* Slide 9: Next Steps */}
        <Slide backgroundColor="white">
          <FlexBox height="100%" width="100%" justifyContent="center" alignItems="center">
            <NextStepsSlide
              label="今後のアクション"
              keyMessage="成功に向けた具体的なステップ"
              steps={[
                {
                  title: "経営層承認と予算確保",
                  description: "本提案内容の承認を得て、次年度予算への組み込みを行います",
                  deadline: "2025年5月末",
                  responsible: "経営企画部",
                  priority: "high"
                },
                {
                  title: "プロジェクトチーム編成",
                  description: "各部門から選抜メンバーを集め、専門チームを組成します",
                  deadline: "2025年6月15日",
                  responsible: "PMO",
                  priority: "high"
                },
                {
                  title: "詳細要件定義",
                  description: "各部門へのヒアリングを実施し、具体的な要件を整理します",
                  deadline: "2025年7月末",
                  responsible: "IT部門",
                  priority: "medium"
                },
                {
                  title: "ベンダー選定",
                  description: "RFPを作成し、最適なパートナー企業を選定します",
                  deadline: "2025年8月末",
                  responsible: "調達部門",
                  priority: "medium"
                }
              ]}
              callToAction="まずは経営会議での承認を目指し、6月1日に詳細説明会を開催します"
            />
          </FlexBox>
        </Slide>
      </Deck>
    </div>
  );
}
