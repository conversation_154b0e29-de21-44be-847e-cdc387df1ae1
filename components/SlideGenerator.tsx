'use client';

import React, { useState, useRef } from 'react';
import { processAgentResult } from '@/agents';
import { Deck, Slide, FlexBox } from 'spectacle';
import dynamic from 'next/dynamic';
import { mapPropsForComponent } from '@/utils/propsMapper';
import PDFExporter, { SlideWrapper } from '@/components/PDFExporter';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

// スライドコンポーネントの動的インポート
const slideComponents = {
  CoverSlide: dynamic(() => import('@/components/CoverSlide')),
  ExecutiveSummarySlide: dynamic(() => import('@/components/ExecutiveSummarySlide')),
  ProblemAnalysisSlide: dynamic(() => import('@/components/ProblemAnalysisSlide')),
  AsIsSlide: dynamic(() => import('@/components/AsIsSlide')),
  SolutionOverviewSlide: dynamic(() => import('@/components/SolutionOverviewSlide')),
  ROICalculationSlide: dynamic(() => import('@/components/ROICalculationSlide')),
  TimelineSlide: dynamic(() => import('@/components/TimelineSlide')),
  ComparisonTableSlide: dynamic(() => import('@/components/ComparisonTableSlide')),
  MetricsSlide: dynamic(() => import('@/components/MetricsSlide')),
  NextStepsSlide: dynamic(() => import('@/components/NextStepsSlide')),
  CaseInfoSlide: dynamic(() => import('@/components/CaseInfoSlide')),
  VennDiagram: dynamic(() => import('@/components/VennDiagram')),
  AutoCycleDiagram: dynamic(() => import('@/components/AutoCycleDiagram')),
};

const theme = {
  colors: {
    primary: '#143d8f',
    secondary: '#003886',
  },
  fonts: {
    header: '"Noto Sans JP", sans-serif',
    text: '"Noto Sans JP", sans-serif',
  },
};

export default function SlideGenerator() {
  const [meetingNotes, setMeetingNotes] = useState('');
  const [slides, setSlides] = useState<Array<{ component: string; props: any }>>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [showPresentation, setShowPresentation] = useState(false);
  const [isExportingPDF, setIsExportingPDF] = useState(false);
  const [showPDFPreview, setShowPDFPreview] = useState(false);
  const pdfExportRef = useRef<HTMLDivElement>(null);

  const handleGenerateSlides = async () => {
    if (!meetingNotes.trim()) {
      setErrors(['議事録を入力してください。']);
      return;
    }

    setIsGenerating(true);
    setErrors([]);

    try {
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ meetingNotes }),
      });

      if (!response.ok) {
        throw new Error('スライド生成に失敗しました');
      }

      const result = await response.json();
      const processed = processAgentResult(result);
      
      // 詳細デバッグ用：生成されたスライドデータをコンソールに出力
      console.log('=== 詳細デバッグ情報 ===');
      console.log('API Response:', result);
      console.log('Processed slides:', processed.slides);
      console.log('=== 個別スライドprops ===');
      processed.slides.forEach((slide, index) => {
        console.log(`Slide ${index} (${slide.component}):`, slide.props);
      });
      
      if (processed.errors.length > 0) {
        setErrors(processed.errors);
      } else {
        setSlides(processed.slides);
        setShowPresentation(true);
      }
    } catch (error) {
      setErrors(['スライド生成中にエラーが発生しました。']);
      console.error(error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleExportToPDF = async () => {
    if (!pdfExportRef.current || slides.length === 0) return;

    setIsExportingPDF(true);
    try {
      // A4サイズの設定（横向き）
      const pdf = new jsPDF('landscape', 'mm', 'a4');
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();

      // スライドコンテナを取得
      const slideElements = pdfExportRef.current.querySelectorAll('[data-slide]');
      
      if (slideElements.length === 0) {
        throw new Error('スライドが見つかりません');
      }

      for (let i = 0; i < slideElements.length; i++) {
        const slideElement = slideElements[i] as HTMLElement;
        
        // スライドをキャンバスに変換
        const canvas = await html2canvas(slideElement, {
          scale: 2, // 高解像度
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff',
          width: slideElement.offsetWidth,
          height: slideElement.offsetHeight,
        });

        // キャンバスを画像データに変換
        const imgData = canvas.toDataURL('image/png');
        
        // 新しいページを追加（最初のスライド以外）
        if (i > 0) {
          pdf.addPage();
        }

        // 画像をPDFに追加（アスペクト比を維持しながらページに収める）
        const imgWidth = canvas.width;
        const imgHeight = canvas.height;
        const ratio = Math.min(pageWidth / imgWidth, pageHeight / imgHeight);
        
        const finalWidth = imgWidth * ratio;
        const finalHeight = imgHeight * ratio;
        
        // ページの中央に配置
        const x = (pageWidth - finalWidth) / 2;
        const y = (pageHeight - finalHeight) / 2;

        pdf.addImage(imgData, 'PNG', x, y, finalWidth, finalHeight);
      }

      // PDFを保存
      const currentDate = new Date().toISOString().split('T')[0];
      pdf.save(`presentation-${currentDate}.pdf`);

    } catch (error) {
      console.error('PDF export error:', error);
      setErrors(['PDF出力中にエラーが発生しました。']);
    } finally {
      setIsExportingPDF(false);
    }
  };

  const renderSlideForPDF = (slideData: { component: string; props: any }, index: number) => {
    const Component = slideComponents[slideData.component as keyof typeof slideComponents];
    
    if (!Component) {
      return (
        <SlideWrapper key={index} slideIndex={index}>
          <div style={{ padding: '20px', textAlign: 'center' }}>
            <h2 style={{ color: '#d32f2f', marginBottom: '10px' }}>
              コンポーネントが見つかりません
            </h2>
            <p style={{ color: '#666' }}>
              コンポーネント: {slideData.component}
            </p>
          </div>
        </SlideWrapper>
      );
    }

    const mappedProps = mapPropsForComponent(slideData.component, slideData.props);
    
    return (
      <SlideWrapper key={index} slideIndex={index}>
        <Component {...mappedProps} />
      </SlideWrapper>
    );
  };

  const renderSlide = (slideData: { component: string; props: any }, index: number) => {
    // ハイブリッド方式では基本的にコンポーネント名は正確に生成されるが、念のためフォールバック
    const Component = slideComponents[slideData.component as keyof typeof slideComponents];
    
    if (!Component) {
      console.error(`Unknown component: ${slideData.component}`);
      return (
        <Slide key={index} backgroundColor="white">
          <FlexBox height="100%" width="100%" justifyContent="center" alignItems="center">
            <div style={{ padding: '20px', textAlign: 'center' }}>
              <h2 style={{ color: '#d32f2f', marginBottom: '10px' }}>
                コンポーネントが見つかりません
              </h2>
              <p style={{ color: '#666' }}>
                コンポーネント: {slideData.component}
              </p>
              <p style={{ color: '#666', fontSize: '12px', marginTop: '10px' }}>
                ハイブリッド方式で正確なコンポーネント名が生成されるはずです
              </p>
            </div>
          </FlexBox>
        </Slide>
      );
    }

    // AIが生成したpropsを適切な形式にマッピング
    const mappedProps = mapPropsForComponent(slideData.component, slideData.props);
    
    console.log(`Rendering ${slideData.component} with mapped props:`, mappedProps);

    return (
      <Slide key={index} backgroundColor="white">
        <FlexBox height="100%" width="100%" justifyContent="center" alignItems="center">
          <Component {...mappedProps} />
        </FlexBox>
      </Slide>
    );
  };

  if (showPresentation && slides.length > 0) {
    return (
      <div className="relative w-full h-screen">
        <div className="absolute top-4 right-4 z-50 flex gap-2">
          <button
            onClick={handleExportToPDF}
            disabled={isExportingPDF}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              isExportingPDF
                ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                : 'bg-red-600 text-white hover:bg-red-700'
            }`}
          >
            {isExportingPDF ? 'PDF出力中...' : 'PDFで出力'}
          </button>
          <button
            onClick={() => setShowPresentation(false)}
            className="bg-gray-800 text-white px-4 py-2 rounded-lg hover:bg-gray-700"
          >
            編集に戻る
          </button>
        </div>
        
        {/* PDF出力用の隠しコンテナ */}
        <div 
          ref={pdfExportRef}
          style={{ 
            position: 'absolute', 
            left: '-9999px', 
            top: '0',
            zIndex: -1
          }}
        >
          {slides?.map((slide, index) => renderSlideForPDF(slide, index))}
        </div>
        
        <div style={{ height: '100vh' }}>
          <Deck theme={theme}>
            {slides?.map((slide, index) => renderSlide(slide, index))}
          </Deck>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-8">
          AI スライドジェネレーター
        </h1>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="mb-6">
            <label htmlFor="meetingNotes" className="block text-sm font-medium text-gray-700 mb-2">
              議事録を入力してください
            </label>
            <textarea
              id="meetingNotes"
              value={meetingNotes}
              onChange={(e) => setMeetingNotes(e.target.value)}
              placeholder="例: 株式会社○○様との商談議事録\n\n日時: 2024年5月24日\n参加者: ○○様、△△様\n\n議題: 海外展開プロジェクトについて\n\n現状の課題:\n- 市場調査が不足している\n- 現地パートナーが見つからない\n- 規制対応が複雑\n\n提案内容:\n- 市場調査サービスの提供\n- パートナー企業の紹介\n- 規制対応コンサルティング\n\n期待される効果:\n- 3ヶ月で市場参入準備完了\n- 初年度売上10億円見込み\n\n次のステップ:\n- 詳細提案書の作成\n- 見積もりの提示"
              className="w-full h-64 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {errors.length > 0 && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
              <h3 className="text-sm font-medium text-red-800 mb-2">エラー:</h3>
              <ul className="list-disc list-inside text-sm text-red-600">
                {errors?.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}

          <div className="flex justify-between items-center">
            <button
              onClick={handleGenerateSlides}
              disabled={isGenerating}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                isGenerating
                  ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              {isGenerating ? 'スライドを生成中...' : 'スライドを生成'}
            </button>
            
            {slides.length > 0 && (
              <button
                onClick={() => setShowPresentation(true)}
                className="px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors"
              >
                プレゼンテーションを表示
              </button>
            )}
          </div>
        </div>

        {/* 使い方ガイド */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-3">使い方</h2>
          <ol className="list-decimal list-inside text-sm text-gray-700 space-y-2">
            <li>議事録や商談内容をテキストエリアに入力してください</li>
            <li>「スライドを生成」ボタンをクリックすると、AIが自動的にスライドを作成します</li>
            <li>生成されたスライドは「プレゼンテーションを表示」ボタンで確認できます</li>
            <li>プレゼンテーション画面で「PDFで出力」ボタンをクリックすると、スライドをPDFファイルとしてダウンロードできます</li>
            <li>スライドは後から編集・カスタマイズすることができます</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
