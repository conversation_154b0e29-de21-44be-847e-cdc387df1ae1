import React from 'react';
import { ComparisonTableSlideProps } from '@/types/slide-props';
import SlideLabel from './SlideParts/SlideLabel';
import SlideKeyMessage from './SlideParts/SlideKeyMessage';

export default function ComparisonTableSlide({
  label,
  keyMessage,
  headers,
  rows,
  highlightColumn
}: ComparisonTableSlideProps) {
  return (
    <div className="absolute inset-0 w-full h-full bg-white font-sans">
      <SlideLabel label={label} />
      <SlideKeyMessage keyMessage={keyMessage} />

      <div className="pt-40 px-8 w-full">
        <div className="max-w-6xl mx-auto">
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr>
                  {headers?.map((header, idx) => (
                    <th
                      key={idx}
                      className={`
                        px-4 py-3 text-left font-bold text-white
                        ${idx === 0 ? 'bg-gray-600 rounded-tl-lg' : ''}
                        ${idx === headers.length - 1 ? 'rounded-tr-lg' : ''}
                        ${highlightColumn === idx ? 'bg-blue-600' : idx === 0 ? 'bg-gray-600' : 'bg-gray-500'}
                      `}
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {rows?.map((row, rowIdx) => (
                  <tr key={rowIdx} className={rowIdx % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                    {/* Row label */}
                    <td className="px-4 py-3 font-medium text-gray-800 border-b border-gray-200">
                      {row.label}
                    </td>
                    
                    {/* Values */}
                    {row.values?.map((value, colIdx) => {
                      const isHighlighted = typeof value === 'object' ? value.highlight : false;
                      const text = typeof value === 'object' ? value.text : value;
                      
                      return (
                        <td
                          key={colIdx}
                          className={`
                            px-4 py-3 border-b border-gray-200
                            ${highlightColumn === colIdx + 1 ? 'bg-blue-50 font-medium' : ''}
                            ${isHighlighted ? 'text-blue-600 font-bold' : 'text-gray-700'}
                          `}
                        >
                          {isHighlighted && (
                            <span className="inline-block w-2 h-2 bg-blue-600 rounded-full mr-2"></span>
                          )}
                          {text}
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Legend if highlight column is specified */}
          {highlightColumn !== undefined && (
            <div className="mt-6 flex items-center justify-center">
              <div className="bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
                <span className="text-sm text-blue-700 font-medium">
                  ★ 推奨オプション
                </span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
