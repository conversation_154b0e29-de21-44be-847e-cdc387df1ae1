import React, { useEffect } from 'react';
import { CoverSlideProps } from '@/types/props';
import Image from 'next/image';

export default function CoverSlide({
  companyName = '',
  mainTitle,
  subTitle,
  date = '',
  logoSrc = '/assets/cover_logo.png',
  // AIが生成する可能性のあるフィールド名に対応
  title,
  subtitle,
  ...otherProps
}: CoverSlideProps & { 
  title?: string; 
  subtitle?: string;
  [key: string]: any;
}) {
  // デバッグ用：propsの内容をログ出力
  useEffect(() => {
    console.log('CoverSlide props:', {
      companyName,
      mainTitle,
      subTitle,
      date,
      title,
      subtitle,
      otherProps
    });
  }, [companyName, mainTitle, subTitle, date, title, subtitle, otherProps]);

  // AIが生成したフィールド名を適切にマッピング
  const actualMainTitle = mainTitle || title || 'タイトル未設定';
  const actualSubTitle = subTitle || subtitle || '';

  return (
    <div className="relative w-full h-full flex flex-col items-center justify-center px-8 md:px-16">
      {/* Left top badge */}
      <div className="absolute top-0 left-0 bg-primary text-white text-sm font-semibold px-4 py-1.5 rounded-sm">
        {companyName} 御中
      </div>

      {/* Main title */}
      <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 text-center">
        {actualMainTitle}
      </h1>

      {/* Underline */}
      <div className="w-48 md:w-72 h-0.5 bg-primary mt-0"></div>

      {/* Subtitle */}
      <h2 className="text-base md:text-lg font-medium text-gray-700 mt-3 text-center">
        {actualSubTitle}
      </h2>

      {/* Date */}
      <div className="absolute bottom-12 md:bottom-15 text-sm text-gray-600">
        {date}
      </div>

      {/* Logo */}
      <div className="absolute bottom-0 flex items-center justify-center">
        <img
          src={logoSrc}
          alt="company logo"
          className="h-12 md:h-16 object-contain select-none"
        />
      </div>
    </div>
  );
}
