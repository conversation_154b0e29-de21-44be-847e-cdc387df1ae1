'use client';

import React, { useState } from 'react';
import SlideGenerator from '@/components/SlideGenerator';
import SamplePresentation from '@/components/SamplePresentation';

type TabType = 'home' | 'generator' | 'templates' | 'sample';

export default function MainApp() {
  const [activeTab, setActiveTab] = useState<TabType>('home');

  const tabs = [
    { id: 'home', label: 'ホーム', icon: '🏠' },
    { id: 'generator', label: 'AI生成', icon: '🤖' },
    { id: 'templates', label: 'テンプレート', icon: '📋' },
    { id: 'sample', label: 'サンプル', icon: '👁️' },
  ] as const;

  const renderContent = () => {
    switch (activeTab) {
      case 'home':
        return <HomeContent setActiveTab={setActiveTab} />;
      case 'generator':
        return <SlideGenerator />;
      case 'templates':
        return <TemplatesContent setActiveTab={setActiveTab} />;
      case 'sample':
        return <SamplePresentation />;
      default:
        return <HomeContent setActiveTab={setActiveTab} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation Header */}
      <nav className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-gray-800">
                Spectacle Claude
              </h1>
            </div>
            
            {/* Tab Navigation */}
            <div className="flex space-x-1">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2
                    ${activeTab === tab.id
                      ? 'bg-blue-600 text-white shadow-md'
                      : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                    }
                  `}
                >
                  <span>{tab.icon}</span>
                  <span className="hidden sm:inline">{tab.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      </nav>

      {/* Content */}
      <main className="transition-all duration-300">
        {renderContent()}
      </main>
    </div>
  );
}

// Home Content Component
function HomeContent({ setActiveTab }: { setActiveTab: (tab: TabType) => void }) {
  return (
    <div className="bg-gradient-to-br from-blue-50 to-purple-50 p-8">
      <div className="max-w-6xl mx-auto">
        <header className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-800 mb-4">
            Spectacle Claude
          </h1>
          <p className="text-xl text-gray-600">
            AIが議事録から自動でプレゼンテーションを生成
          </p>
        </header>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {/* AIスライドジェネレーター */}
          <button
            onClick={() => setActiveTab('generator')}
            className="group bg-white rounded-xl shadow-lg p-8 hover:shadow-2xl transition-all duration-300 text-left"
          >
            <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mb-6">
              <span className="text-2xl">🤖</span>
            </div>
            <h2 className="text-2xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors">
              AIスライドジェネレーター
            </h2>
            <p className="text-gray-600 mb-4">
              議事録を入力するだけで、AIが自動的にプレゼンテーション資料を作成します。
            </p>
            <div className="text-blue-600 font-medium group-hover:translate-x-2 transition-transform inline-block">
              使ってみる →
            </div>
          </button>

          {/* テンプレート */}
          <button
            onClick={() => setActiveTab('templates')}
            className="group bg-white rounded-xl shadow-lg p-8 hover:shadow-2xl transition-all duration-300 text-left"
          >
            <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mb-6">
              <span className="text-2xl">📋</span>
            </div>
            <h2 className="text-2xl font-bold text-gray-800 mb-3 group-hover:text-green-600 transition-colors">
              スライドテンプレート
            </h2>
            <p className="text-gray-600 mb-4">
              様々な用途に使える豊富なスライドテンプレートから選択できます。
            </p>
            <div className="text-green-600 font-medium group-hover:translate-x-2 transition-transform inline-block">
              テンプレートを見る →
            </div>
          </button>

          {/* サンプルプレゼンテーション */}
          <button
            onClick={() => setActiveTab('sample')}
            className="group bg-white rounded-xl shadow-lg p-8 hover:shadow-2xl transition-all duration-300 text-left"
          >
            <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mb-6">
              <span className="text-2xl">👁️</span>
            </div>
            <h2 className="text-2xl font-bold text-gray-800 mb-3 group-hover:text-purple-600 transition-colors">
              サンプルプレゼンテーション
            </h2>
            <p className="text-gray-600 mb-4">
              作成可能なスライドコンポーネントのサンプルを確認できます。
            </p>
            <div className="text-purple-600 font-medium group-hover:translate-x-2 transition-transform inline-block">
              サンプルを見る →
            </div>
          </button>
        </div>

        <section className="bg-white rounded-xl shadow-lg p-10">
          <h2 className="text-3xl font-bold text-gray-800 mb-6 text-center">
            特徴
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-xl">⚡</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">高速生成</h3>
              <p className="text-gray-600 text-sm">
                議事録を入力して数秒でプレゼンテーション資料が完成
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-xl">📊</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">豊富なテンプレート</h3>
              <p className="text-gray-600 text-sm">
                様々な用途に対応した20種類以上のスライドコンポーネント
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-xl">✏️</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">編集可能</h3>
              <p className="text-gray-600 text-sm">
                生成後も自由に編集・カスタマイズが可能
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-xl">📄</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">PDF出力</h3>
              <p className="text-gray-600 text-sm">
                作成したスライドを高品質PDFとして出力可能
              </p>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}

// Templates Content Component
function TemplatesContent({ setActiveTab }: { setActiveTab: (tab: TabType) => void }) {
  const templates = [
    {
      name: 'カバースライド',
      component: 'CoverSlide',
      description: 'プレゼンテーションの表紙用スライド',
      icon: '📄',
      category: '基本'
    },
    {
      name: 'エグゼクティブサマリー',
      component: 'ExecutiveSummarySlide',
      description: '要約と重要ポイントを整理',
      icon: '📋',
      category: '基本'
    },
    {
      name: '問題分析',
      component: 'ProblemAnalysisSlide',
      description: '課題の分析と整理',
      icon: '🔍',
      category: '分析'
    },
    {
      name: '現状分析',
      component: 'AsIsSlide',
      description: '現在の状況を可視化',
      icon: '📊',
      category: '分析'
    },
    {
      name: 'ソリューション概要',
      component: 'SolutionOverviewSlide',
      description: '解決策の全体像を説明',
      icon: '💡',
      category: '提案'
    },
    {
      name: 'ROI計算',
      component: 'ROICalculationSlide',
      description: '投資対効果を数値で示す',
      icon: '💰',
      category: '財務'
    },
    {
      name: 'タイムライン',
      component: 'TimelineSlide',
      description: 'スケジュールと工程を表示',
      icon: '📅',
      category: '計画'
    },
    {
      name: '比較表',
      component: 'ComparisonTableSlide',
      description: '複数の選択肢を比較',
      icon: '⚖️',
      category: '比較'
    },
    {
      name: 'メトリクス',
      component: 'MetricsSlide',
      description: 'KPIや成果指標を表示',
      icon: '📈',
      category: '評価'
    },
    {
      name: '次のステップ',
      component: 'NextStepsSlide',
      description: '今後のアクションプランを整理',
      icon: '👆',
      category: '計画'
    },
    {
      name: 'ケース情報',
      component: 'CaseInfoSlide',
      description: '事例や詳細情報を整理',
      icon: '📂',
      category: '詳細'
    },
    {
      name: 'ベン図',
      component: 'VennDiagram',
      description: '関係性をベン図で表現',
      icon: '🔵',
      category: '図表'
    },
    {
      name: 'サイクル図',
      component: 'AutoCycleDiagram',
      description: '循環的なプロセスを図解',
      icon: '🔄',
      category: '図表'
    }
  ];

  const categories = ['全て', '基本', '分析', '提案', '財務', '計画', '比較', '評価', '詳細', '図表'];
  const [selectedCategory, setSelectedCategory] = useState('全て');

  const filteredTemplates = selectedCategory === '全て' 
    ? templates 
    : templates.filter(template => template.category === selectedCategory);

  return (
    <div className="p-8">
      <div className="max-w-7xl mx-auto">
        <header className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-4">
            スライドテンプレート
          </h1>
          <p className="text-lg text-gray-600">
            用途に応じたスライドテンプレートを選択して、効率的にプレゼンテーションを作成できます。
          </p>
        </header>

        {/* Category Filter */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`
                  px-4 py-2 rounded-full text-sm font-medium transition-all duration-200
                  ${selectedCategory === category
                    ? 'bg-blue-600 text-white shadow-md'
                    : 'bg-white text-gray-600 border border-gray-300 hover:border-blue-300 hover:text-blue-600'
                  }
                `}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredTemplates.map((template) => (
            <div
              key={template.component}
              className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200"
            >
              <div className="text-center mb-4">
                <div className="text-4xl mb-2">{template.icon}</div>
                <h3 className="font-semibold text-gray-800 mb-1">
                  {template.name}
                </h3>
                <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">
                  {template.category}
                </span>
              </div>
              <p className="text-sm text-gray-600 mb-4 text-center">
                {template.description}
              </p>
              <button
                onClick={() => setActiveTab('sample')}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-sm"
              >
                サンプルを見る
              </button>
            </div>
          ))}
        </div>

        {/* Quick Start */}
        <div className="mt-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-8 text-white">
          <h2 className="text-2xl font-bold mb-4">今すぐ始める</h2>
          <p className="mb-6">
            議事録を入力してAIが最適なテンプレートを選択し、自動でスライドを生成します。
          </p>
          <button
            onClick={() => setActiveTab('generator')}
            className="bg-white text-blue-600 font-medium py-3 px-6 rounded-lg hover:bg-gray-100 transition-colors"
          >
            AI生成を試す
          </button>
        </div>
      </div>
    </div>
  );
}
