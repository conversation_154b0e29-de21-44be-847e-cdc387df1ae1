import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON> } from '@/types/props';

export default function SlideLabel({ label = '' }: HasLabel) {
  if (!label) {
    return null;
  }

  return (
    <section className="w-full bg-white p-8 space-y-10 font-sans text-left absolute top-0 left-0">
      <div className="flex items-center space-x-3">
        <span className="w-4 h-6 bg-primary-dark" />
        <h3 className="text-primary-dark text-xl font-bold">{label}</h3>
      </div>
    </section>
  );
}