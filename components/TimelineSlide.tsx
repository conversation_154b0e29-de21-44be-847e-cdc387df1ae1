import React from 'react';
import { TimelineSlideProps } from '@/types/slide-props';
import SlideLabel from './SlideParts/SlideLabel';
import SlideKeyMessage from './SlideParts/SlideKeyMessage';

export default function TimelineSlide({
  label,
  keyMessage,
  milestones,
  orientation = 'horizontal'
}: TimelineSlideProps) {
  const getStatusColor = (status?: 'completed' | 'in-progress' | 'planned') => {
    switch (status) {
      case 'completed': return 'bg-green-500';
      case 'in-progress': return 'bg-yellow-500';
      case 'planned': return 'bg-gray-400';
      default: return 'bg-blue-500';
    }
  };

  const getStatusText = (status?: 'completed' | 'in-progress' | 'planned') => {
    switch (status) {
      case 'completed': return '完了';
      case 'in-progress': return '進行中';
      case 'planned': return '予定';
      default: return '';
    }
  };

  return (
    <div className="absolute inset-0 w-full h-full bg-white font-sans">
      <SlideLabel label={label} />
      <SlideKeyMessage keyMessage={keyMessage} />

      <div className="pt-40 px-8 w-full h-full">
        <div className="max-w-6xl mx-auto h-full flex items-center">
          {orientation === 'horizontal' ? (
            // Horizontal Timeline
            <div className="w-full">
              <div className="relative">
                {/* Timeline Line */}
                <div className="absolute top-12 left-0 right-0 h-1 bg-gray-300"></div>
                
                {/* Milestones */}
                <div className="relative grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {milestones?.map((milestone, idx) => (
                    <div key={idx} className="relative">
                      {/* Connector dot */}
                      <div className={`absolute top-10 left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full ${getStatusColor(milestone.status)} border-4 border-white shadow-md z-10`}></div>
                      
                      {/* Content */}
                      <div className="pt-20">
                        <div className="bg-gray-50 rounded-lg p-4 shadow-sm">
                          <p className="text-sm font-bold text-gray-600 mb-1">
                            {milestone.date}
                          </p>
                          <h4 className="text-base font-bold text-gray-800 mb-2">
                            {milestone.title}
                          </h4>
                          {milestone.description && (
                            <p className="text-sm text-gray-600">
                              {milestone.description}
                            </p>
                          )}
                          {milestone.status && (
                            <span className={`inline-block mt-2 text-xs px-2 py-1 rounded-full text-white ${getStatusColor(milestone.status)}`}>
                              {getStatusText(milestone.status)}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            // Vertical Timeline
            <div className="w-full max-w-3xl mx-auto">
              <div className="relative">
                {/* Timeline Line */}
                <div className="absolute top-0 bottom-0 left-1/2 transform -translate-x-1/2 w-1 bg-gray-300"></div>
                
                {/* Milestones */}
                <div className="space-y-8">
                  {milestones?.map((milestone, idx) => (
                    <div key={idx} className={`relative flex ${idx % 2 === 0 ? 'justify-start' : 'justify-end'}`}>
                      {/* Connector dot */}
                      <div className={`absolute top-8 left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full ${getStatusColor(milestone.status)} border-4 border-white shadow-md z-10`}></div>
                      
                      {/* Content */}
                      <div className={`w-5/12 ${idx % 2 === 0 ? 'pr-8 text-right' : 'pl-8'}`}>
                        <div className="bg-gray-50 rounded-lg p-4 shadow-sm">
                          <p className="text-sm font-bold text-gray-600 mb-1">
                            {milestone.date}
                          </p>
                          <h4 className="text-base font-bold text-gray-800 mb-2">
                            {milestone.title}
                          </h4>
                          {milestone.description && (
                            <p className="text-sm text-gray-600">
                              {milestone.description}
                            </p>
                          )}
                          {milestone.status && (
                            <span className={`inline-block mt-2 text-xs px-2 py-1 rounded-full text-white ${getStatusColor(milestone.status)}`}>
                              {getStatusText(milestone.status)}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
