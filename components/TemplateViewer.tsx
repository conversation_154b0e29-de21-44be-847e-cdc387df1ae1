'use client';

import React, { useState } from 'react';
import { Deck, Slide, FlexBox } from 'spectacle';
import dynamic from 'next/dynamic';

// スライドコンポーネントの動的インポート
const slideComponents = {
  CoverSlide: dynamic(() => import('@/components/CoverSlide')),
  ExecutiveSummarySlide: dynamic(() => import('@/components/ExecutiveSummarySlide')),
  ProblemAnalysisSlide: dynamic(() => import('@/components/ProblemAnalysisSlide')),
  AsIsSlide: dynamic(() => import('@/components/AsIsSlide')),
  SolutionOverviewSlide: dynamic(() => import('@/components/SolutionOverviewSlide')),
  ROICalculationSlide: dynamic(() => import('@/components/ROICalculationSlide')),
  TimelineSlide: dynamic(() => import('@/components/TimelineSlide')),
  ComparisonTableSlide: dynamic(() => import('@/components/ComparisonTableSlide')),
  MetricsSlide: dynamic(() => import('@/components/MetricsSlide')),
  NextStepsSlide: dynamic(() => import('@/components/NextStepsSlide')),
  CaseInfoSlide: dynamic(() => import('@/components/CaseInfoSlide')),
  VennDiagram: dynamic(() => import('@/components/VennDiagram')),
  AutoCycleDiagram: dynamic(() => import('@/components/AutoCycleDiagram')),
  CycleDiagram: dynamic(() => import('@/components/CycleDiagram')),
};

const theme = {
  colors: {
    primary: '#143d8f',
    secondary: '#003886',
  },
  fonts: {
    header: '"Noto Sans JP", sans-serif',
    text: '"Noto Sans JP", sans-serif',
  },
};

// サンプルプロップス
const sampleProps = {
  CoverSlide: {
    companyName: "株式会社サンプル",
    mainTitle: "カバースライドのサンプル",
    subTitle: "プレゼンテーションの表紙として使用",
    date: "2025年5月28日"
  },
  ExecutiveSummarySlide: {
    label: "エグゼクティブサマリー",
    keyMessage: "重要なポイントを要約",
    sections: [
      {
        title: "現状の課題",
        points: ["課題1", "課題2", "課題3"],
        icon: "⚠️"
      },
      {
        title: "提案ソリューション",
        points: ["解決策1", "解決策2", "解決策3"],
        icon: "💡"
      },
      {
        title: "期待される効果",
        points: ["効果1", "効果2", "効果3"],
        icon: "📈"
      }
    ]
  },
  ProblemAnalysisSlide: {
    label: "現状分析",
    keyMessage: "解決すべき重要課題",
    problems: [
      {
        category: "業務効率",
        description: "手作業による業務が多く、効率が悪い",
        impact: "high",
        details: ["詳細1", "詳細2"]
      },
      {
        category: "データ管理",
        description: "データが分散しており管理が困難",
        impact: "medium",
        details: ["詳細A", "詳細B"]
      }
    ],
    futureProjection: {
      title: "将来への影響",
      description: "このまま放置した場合の問題点や影響について説明します。"
    }
  },
  AsIsSlide: {
    label: "現状分析",
    keyMessage: "現在の状況を可視化",
    currentState: {
      title: "現在の状況",
      description: "現状の問題点や課題を整理"
    },
    issues: [
      { category: "効率性", description: "作業効率の問題", severity: "high" },
      { category: "品質", description: "品質管理の課題", severity: "medium" }
    ]
  },
  SolutionOverviewSlide: {
    label: "ソリューション概要",
    keyMessage: "統合的なアプローチ",
    solutions: [
      {
        problemCategory: "業務効率",
        solution: "自動化システムの導入",
        benefits: ["効率向上", "エラー削減"]
      },
      {
        problemCategory: "データ管理",
        solution: "統合プラットフォーム",
        benefits: ["一元管理", "リアルタイム分析"]
      }
    ],
    centralConcept: "統合ソリューション"
  },
  ROICalculationSlide: {
    label: "投資対効果",
    keyMessage: "投資回収期間の算出",
    investment: [
      { item: "システム開発", amount: 50000000 },
      { item: "導入費用", amount: 20000000 }
    ],
    returns: [
      { item: "コスト削減", amount: 30000000, timeframe: "年間" },
      { item: "収益向上", amount: 40000000, timeframe: "年間" }
    ],
    totalROI: {
      percentage: 85,
      paybackPeriod: "12ヶ月"
    },
    notes: ["3年間の効果で算出"]
  },
  TimelineSlide: {
    label: "実施スケジュール",
    keyMessage: "段階的な導入計画",
    milestones: [
      {
        date: "2025年6月",
        title: "フェーズ1開始",
        description: "要件定義・設計",
        status: "planned"
      },
      {
        date: "2025年9月",
        title: "フェーズ2開始",
        description: "開発・テスト",
        status: "planned"
      },
      {
        date: "2025年12月",
        title: "本稼働",
        description: "システム稼働開始",
        status: "planned"
      }
    ]
  },
  ComparisonTableSlide: {
    label: "比較検討",
    keyMessage: "最適な選択肢の選定",
    headers: ["項目", "案A", "案B", "案C"],
    rows: [
      {
        label: "コスト",
        values: ["低", "中", "高"]
      },
      {
        label: "効果",
        values: [{ text: "高", highlight: true }, "中", "低"]
      },
      {
        label: "期間",
        values: ["短", { text: "中", highlight: true }, "長"]
      }
    ],
    highlightColumn: 1
  },
  MetricsSlide: {
    label: "重要指標",
    keyMessage: "KPIによる成果測定",
    metrics: [
      {
        label: "効率向上率",
        value: 85,
        unit: "%",
        target: 80,
        trend: "up",
        description: "業務効率の改善度"
      },
      {
        label: "コスト削減",
        value: 2.5,
        unit: "億円",
        target: 2.0,
        trend: "up",
        description: "年間コスト削減額"
      },
      {
        label: "満足度",
        value: 90,
        unit: "%",
        target: 85,
        trend: "stable",
        description: "ユーザー満足度"
      }
    ]
  },
  NextStepsSlide: {
    label: "今後のアクション",
    keyMessage: "成功に向けた具体的ステップ",
    steps: [
      {
        title: "承認・予算確保",
        description: "経営層の承認を得て予算を確保します",
        deadline: "2025年6月末",
        responsible: "企画部",
        priority: "high"
      },
      {
        title: "チーム編成",
        description: "プロジェクトチームを組成します",
        deadline: "2025年7月15日",
        responsible: "人事部",
        priority: "high"
      }
    ],
    callToAction: "まずは詳細計画の策定から始めましょう"
  },
  CaseInfoSlide: {
    label: "ケース情報",
    keyMessage: "詳細情報の整理",
    caseInfo: {
      title: "プロジェクト概要",
      details: [
        { label: "期間", value: "6ヶ月" },
        { label: "予算", value: "1億円" },
        { label: "体制", value: "10名" }
      ]
    },
    additionalInfo: "その他の重要な情報や注意事項について説明します。"
  },
  VennDiagram: {
    label: "関係性分析",
    keyMessage: "要素間の関係を可視化",
    circles: [
      { label: "技術", color: "#FF6B6B" },
      { label: "ビジネス", color: "#4ECDC4" },
      { label: "ユーザー", color: "#45B7D1" }
    ],
    intersections: [
      { areas: ["技術", "ビジネス"], label: "実用性" },
      { areas: ["ビジネス", "ユーザー"], label: "価値" },
      { areas: ["技術", "ユーザー"], label: "利便性" },
      { areas: ["技術", "ビジネス", "ユーザー"], label: "イノベーション" }
    ]
  },
  AutoCycleDiagram: {
    label: "プロセス図",
    keyMessage: "循環的なプロセスの可視化",
    steps: [
      { label: "計画", description: "目標設定と計画立案" },
      { label: "実行", description: "計画の実施" },
      { label: "評価", description: "結果の検証" },
      { label: "改善", description: "課題の改善" }
    ],
    centerLabel: "PDCA"
  },
  CycleDiagram: {
    label: "サイクル図",
    keyMessage: "継続的な改善サイクル",
    steps: [
      { label: "分析", description: "現状分析" },
      { label: "設計", description: "解決策設計" },
      { label: "実装", description: "システム実装" },
      { label: "運用", description: "運用・保守" }
    ],
    centerLabel: "改善"
  }
};

type TemplateViewerProps = {
  setActiveTab: (tab: 'home' | 'generator' | 'templates' | 'sample') => void;
};

export default function TemplateViewer({ setActiveTab }: TemplateViewerProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState('全て');

  const templates = [
    {
      name: 'カバースライド',
      component: 'CoverSlide',
      description: 'プレゼンテーションの表紙用スライド',
      icon: '📄',
      category: '基本'
    },
    {
      name: 'エグゼクティブサマリー',
      component: 'ExecutiveSummarySlide',
      description: '要約と重要ポイントを整理',
      icon: '📋',
      category: '基本'
    },
    {
      name: '問題分析',
      component: 'ProblemAnalysisSlide',
      description: '課題の分析と整理',
      icon: '🔍',
      category: '分析'
    },
    {
      name: '現状分析',
      component: 'AsIsSlide',
      description: '現在の状況を可視化',
      icon: '📊',
      category: '分析'
    },
    {
      name: 'ソリューション概要',
      component: 'SolutionOverviewSlide',
      description: '解決策の全体像を説明',
      icon: '💡',
      category: '提案'
    },
    {
      name: 'ROI計算',
      component: 'ROICalculationSlide',
      description: '投資対効果を数値で示す',
      icon: '💰',
      category: '財務'
    },
    {
      name: 'タイムライン',
      component: 'TimelineSlide',
      description: 'スケジュールと工程を表示',
      icon: '📅',
      category: '計画'
    },
    {
      name: '比較表',
      component: 'ComparisonTableSlide',
      description: '複数の選択肢を比較',
      icon: '⚖️',
      category: '比較'
    },
    {
      name: 'メトリクス',
      component: 'MetricsSlide',
      description: 'KPIや成果指標を表示',
      icon: '📈',
      category: '評価'
    },
    {
      name: '次のステップ',
      component: 'NextStepsSlide',
      description: '今後のアクションプランを整理',
      icon: '👆',
      category: '計画'
    },
    {
      name: 'ケース情報',
      component: 'CaseInfoSlide',
      description: '事例や詳細情報を整理',
      icon: '📂',
      category: '詳細'
    },
    {
      name: 'ベン図',
      component: 'VennDiagram',
      description: '関係性をベン図で表現',
      icon: '🔵',
      category: '図表'
    },
    {
      name: 'オートサイクル図',
      component: 'AutoCycleDiagram',
      description: '自動循環プロセスを図解',
      icon: '🔄',
      category: '図表'
    },
    {
      name: 'サイクル図',
      component: 'CycleDiagram',
      description: '循環的なプロセスを図解',
      icon: '↻',
      category: '図表'
    }
  ];

  const categories = ['全て', '基本', '分析', '提案', '財務', '計画', '比較', '評価', '詳細', '図表'];

  const filteredTemplates = selectedCategory === '全て' 
    ? templates 
    : templates.filter(template => template.category === selectedCategory);

  const renderTemplatePreview = (templateComponent: string) => {
    const Component = slideComponents[templateComponent as keyof typeof slideComponents];
    const props = sampleProps[templateComponent as keyof typeof sampleProps];
    
    if (!Component || !props) {
      return (
        <div className="flex items-center justify-center h-64 bg-gray-100 rounded-lg">
          <p className="text-gray-500">プレビューを読み込み中...</p>
        </div>
      );
    }

    return (
      <div style={{ height: 'calc(100vh - 64px)', overflow: 'hidden' }}>
        <Deck theme={theme}>
          <Slide backgroundColor="white">
            <FlexBox height="100%" width="100%" justifyContent="center" alignItems="center">
              <Component {...props} />
            </FlexBox>
          </Slide>
        </Deck>
      </div>
    );
  };

  if (selectedTemplate) {
    return (
      <div className="relative">
        <div className="absolute top-4 right-4 z-50 flex gap-2">
          <button
            onClick={() => setSelectedTemplate(null)}
            className="bg-gray-800 text-white px-4 py-2 rounded-lg hover:bg-gray-700"
          >
            一覧に戻る
          </button>
        </div>
        {renderTemplatePreview(selectedTemplate)}
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="max-w-7xl mx-auto">
        <header className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-4">
            スライドテンプレート
          </h1>
          <p className="text-lg text-gray-600">
            用途に応じたスライドテンプレートを選択して、効率的にプレゼンテーションを作成できます。
          </p>
        </header>

        {/* Category Filter */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`
                  px-4 py-2 rounded-full text-sm font-medium transition-all duration-200
                  ${selectedCategory === category
                    ? 'bg-blue-600 text-white shadow-md'
                    : 'bg-white text-gray-600 border border-gray-300 hover:border-blue-300 hover:text-blue-600'
                  }
                `}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredTemplates.map((template) => (
            <div
              key={template.component}
              className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200"
            >
              <div className="text-center mb-4">
                <div className="text-4xl mb-2">{template.icon}</div>
                <h3 className="font-semibold text-gray-800 mb-1">
                  {template.name}
                </h3>
                <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">
                  {template.category}
                </span>
              </div>
              <p className="text-sm text-gray-600 mb-4 text-center">
                {template.description}
              </p>
              <div className="flex gap-2">
                <button
                  onClick={() => setSelectedTemplate(template.component)}
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-sm"
                >
                  プレビュー
                </button>
                <button
                  onClick={() => setActiveTab('sample')}
                  className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors text-sm"
                >
                  完全版
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Quick Start */}
        <div className="mt-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-8 text-white">
          <h2 className="text-2xl font-bold mb-4">今すぐ始める</h2>
          <p className="mb-6">
            議事録を入力してAIが最適なテンプレートを選択し、自動でスライドを生成します。
          </p>
          <button
            onClick={() => setActiveTab('generator')}
            className="bg-white text-blue-600 font-medium py-3 px-6 rounded-lg hover:bg-gray-100 transition-colors"
          >
            AI生成を試す
          </button>
        </div>
      </div>
    </div>
  );
}
