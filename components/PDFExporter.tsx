'use client';

import React, { useRef } from 'react';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

interface PDFExporterProps {
  children: React.ReactNode;
  filename?: string;
  onExportStart?: () => void;
  onExportComplete?: () => void;
  onExportError?: (error: Error) => void;
}

export default function PDFExporter({
  children,
  filename = 'presentation',
  onExportStart,
  onExportComplete,
  onExportError
}: PDFExporterProps) {
  const contentRef = useRef<HTMLDivElement>(null);

  const exportToPDF = async () => {
    if (!contentRef.current) return;

    try {
      onExportStart?.();

      // A4サイズの設定（横向き）
      const pdf = new jsPDF('landscape', 'mm', 'a4');
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();

      // スライドコンテナを取得
      const slideElements = contentRef.current.querySelectorAll('[data-slide]');
      
      if (slideElements.length === 0) {
        throw new Error('スライドが見つかりません');
      }

      for (let i = 0; i < slideElements.length; i++) {
        const slideElement = slideElements[i] as HTMLElement;
        
        // スライドをキャンバスに変換
        const canvas = await html2canvas(slideElement, {
          scale: 2, // 高解像度
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff',
          width: slideElement.offsetWidth,
          height: slideElement.offsetHeight,
        });

        // キャンバスを画像データに変換
        const imgData = canvas.toDataURL('image/png');
        
        // 新しいページを追加（最初のスライド以外）
        if (i > 0) {
          pdf.addPage();
        }

        // 画像をPDFに追加（アスペクト比を維持しながらページに収める）
        const imgWidth = canvas.width;
        const imgHeight = canvas.height;
        const ratio = Math.min(pageWidth / imgWidth, pageHeight / imgHeight);
        
        const finalWidth = imgWidth * ratio;
        const finalHeight = imgHeight * ratio;
        
        // ページの中央に配置
        const x = (pageWidth - finalWidth) / 2;
        const y = (pageHeight - finalHeight) / 2;

        pdf.addImage(imgData, 'PNG', x, y, finalWidth, finalHeight);
      }

      // PDFを保存
      pdf.save(`${filename}.pdf`);
      onExportComplete?.();

    } catch (error) {
      console.error('PDF export error:', error);
      onExportError?.(error as Error);
    }
  };

  return (
    <div>
      <div ref={contentRef}>
        {children}
      </div>
    </div>
  );
}

// スライド用のラッパーコンポーネント
export function SlideWrapper({ children, slideIndex }: { children: React.ReactNode; slideIndex: number }) {
  return (
    <div 
      data-slide={slideIndex}
      style={{
        width: '1024px',
        height: '768px',
        backgroundColor: 'white',
        position: 'relative',
        margin: '20px 0',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center'
      }}
    >
      {children}
    </div>
  );
}
