import React, { useEffect } from 'react';
import { ROICalculationSlideProps } from '@/types/slide-props';
import SlideLabel from './SlideParts/SlideLabel';
import SlideKeyMessage from './SlideParts/SlideKeyMessage';

export default function ROICalculationSlide({
  label = 'ROI計算',
  keyMessage = '',
  investment = [],
  returns = [],
  totalROI = { percentage: 0, paybackPeriod: '' },
  notes = []
}: ROICalculationSlideProps) {
  // デバッグ用：propsの内容をログ出力
  useEffect(() => {
    console.log('ROICalculationSlide props:', {
      label,
      keyMessage,
      investment,
      returns,
      totalROI,
      notes
    });
  }, [label, keyMessage, investment, returns, totalROI, notes]);

  const formatNumber = (num: number) => {
    return num.toLocaleString('ja-JP');
  };

  // 投資コストの合計を安全に計算
  const totalInvestment = investment && investment.length > 0 
    ? investment.reduce((sum, item) => sum + (item.amount || 0), 0)
    : 0;

  // リターンの合計を安全に計算
  const totalReturns = returns && returns.length > 0
    ? returns.reduce((sum, item) => sum + (item.amount || 0), 0)
    : 0;

  // データが不十分な場合のフォールバック表示
  if ((!investment || investment.length === 0) && (!returns || returns.length === 0)) {
    return (
      <div className="absolute inset-0 w-full h-full bg-white font-sans">
        <SlideLabel label={label} />
        <SlideKeyMessage keyMessage={keyMessage} />
        <div className="pt-40 px-8 w-full">
          <div className="flex items-center justify-center h-64">
            <p className="text-gray-500 text-lg">ROIデータがありません</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="absolute inset-0 w-full h-full bg-white font-sans">
      <SlideLabel label={label} />
      <SlideKeyMessage keyMessage={keyMessage} />

      <div className="pt-40 px-8 w-full">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Investment Column */}
            <div className="bg-gradient-to-br from-red-50 to-orange-50 rounded-lg p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                <span className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white text-sm mr-3">
                  投
                </span>
                投資コスト
              </h3>
              <div className="space-y-3">
                {(investment || []).map((item, idx) => (
                  <div key={idx} className="flex justify-between items-center py-2 border-b border-red-100">
                    <span className="text-gray-700">{item.item || '未定義'}</span>
                    <span className="font-bold text-gray-800">
                      {formatNumber(item.amount || 0)}{item.unit || '円'}
                    </span>
                  </div>
                ))}
                {investment && investment.length > 0 && (
                  <div className="pt-3 flex justify-between items-center">
                    <span className="font-bold text-gray-800">合計</span>
                    <span className="text-xl font-bold text-red-600">
                      {formatNumber(totalInvestment)}円
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Returns Column */}
            <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                <span className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm mr-3">
                  収
                </span>
                期待リターン
              </h3>
              <div className="space-y-3">
                {(returns || []).map((item, idx) => (
                  <div key={idx} className="flex justify-between items-center py-2 border-b border-green-100">
                    <div>
                      <span className="text-gray-700">{item.item || '未定義'}</span>
                      {item.timeframe && (
                        <span className="text-xs text-gray-500 ml-2">({item.timeframe})</span>
                      )}
                    </div>
                    <span className="font-bold text-gray-800">
                      {formatNumber(item.amount || 0)}{item.unit || '円'}
                    </span>
                  </div>
                ))}
                {returns && returns.length > 0 && (
                  <div className="pt-3 flex justify-between items-center">
                    <span className="font-bold text-gray-800">合計</span>
                    <span className="text-xl font-bold text-green-600">
                      {formatNumber(totalReturns)}円
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* ROI Summary */}
          <div className="mt-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
            <div className="text-center">
              <h4 className="text-2xl font-bold mb-2">投資対効果（ROI）</h4>
              <div className="text-5xl font-bold mb-2">
                {totalROI?.percentage || 0}%
              </div>
              {totalROI?.paybackPeriod && (
                <p className="text-lg">
                  投資回収期間: <span className="font-bold">{totalROI.paybackPeriod}</span>
                </p>
              )}
            </div>
          </div>

          {/* Notes */}
          {notes && notes.length > 0 && (
            <div className="mt-6 bg-gray-50 rounded-lg p-4">
              <p className="text-xs text-gray-600 font-medium mb-2">注記:</p>
              <ul className="space-y-1">
                {notes.map((note, idx) => (
                  <li key={idx} className="text-xs text-gray-600 flex items-start">
                    <span className="mr-2">※</span>
                    <span>{note}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
