import React from 'react';
import { FlexBox, Text, Box } from 'spectacle';

interface VennDiagramProps {
  leftLabel: string;
  rightLabel: string;
  topLabel?: string;
  leftItems?: string[];
  rightItems?: string[];
  leftPosition?: string;
  rightPosition?: string;
  circleSize?: string;
  circleColor?: string;
  textColor?: string;
}

const VennDiagram: React.FC<VennDiagramProps> = ({ 
  leftLabel, 
  rightLabel, 
  topLabel,
  leftItems = [],
  rightItems = [],
  leftPosition = '40%',
  rightPosition = '60%',
  circleSize = '400px',
  circleColor = 'teal-300',
  textColor = 'white'
}) => (
  <FlexBox position="relative" justifyContent="center" alignItems="center" height="100%" width="100%">
    {/* Left Circle */}
    <Box
      position="absolute"
      style={{ 
        left: leftPosition, 
        transform: 'translateX(-50%)',
        width: circleSize,
        height: circleSize,
        borderRadius: '50%',
        backgroundColor: '#3B82F6',
        opacity: 0.8
      }}
    />

    {/* Right Circle */}
    <Box
      position="absolute"
      style={{ 
        left: rightPosition, 
        transform: 'translateX(-50%)',
        width: circleSize,
        height: circleSize,
        borderRadius: '50%',
        backgroundColor: '#3B82F6',
        opacity: 0.8
      }}
    />

    {/* Left Circle Text */}
    <Box
      position="absolute"
      style={{ 
        left: leftPosition, 
        top: '50%',
        transform: 'translate(-150px, -50%)',
        zIndex: 40
      }}
    >
      <Text color={textColor} fontSize={24} fontWeight="bold">
        {leftLabel}
      </Text>
    </Box>

    {/* Right Circle Text */}
    <Box
      position="absolute"
      style={{ 
        left: rightPosition, 
        top: '50%',
        transform: 'translate(-50px, -50%)',
        zIndex: 40
      }}
    >
      <Text color={textColor} fontSize={24} fontWeight="bold">
        {rightLabel}
      </Text>
    </Box>

    {/* Overlapping Area with & */}
    <Box
      position="absolute"
      top="50%"
      left="50%"
      style={{ 
        transform: 'translate(-50%, -50%)',
        zIndex: 20
      }}
    >
      <Text color="white" fontSize={48} fontWeight="bold">
        &
      </Text>
    </Box>

    {/* Top Label */}
    {topLabel && (
      <Box
        position="absolute"
        top="20px"
        left="50%"
        style={{ 
          transform: 'translateX(-50%)'
        }}
      >
        <Text color="black" fontSize={24} fontWeight="bold">
          {topLabel}
        </Text>
      </Box>
    )}

    {/* Left Items List */}
    {leftItems.length > 0 && (
      <Box
        position="absolute"
        top="120px"
        left="40px"
      >
        <div>
          {leftItems?.map((item, index) => (
            <div key={index} style={{ marginBottom: '8px' }}>
              <Text color="gray.700" fontSize={18}>
                • {item}
              </Text>
            </div>
          ))}
        </div>
      </Box>
    )}

    {/* Right Items List */}
    {rightItems.length > 0 && (
      <Box
        position="absolute"
        top="180px"
        right="40px"
      >
        <div style={{ textAlign: 'right' }}>
          {rightItems?.map((item, index) => (
            <div key={index} style={{ marginBottom: '8px' }}>
              <Text color="gray.700" fontSize={18}>
                {item}
              </Text>
            </div>
          ))}
        </div>
      </Box>
    )}
  </FlexBox>
);

export default VennDiagram; 