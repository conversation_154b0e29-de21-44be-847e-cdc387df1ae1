import React from 'react';
import CycleDiagram from './CycleDiagram';

interface AutoCycleDiagramProps {
  items: string[];
  centerLabel?: string;
  [key: string]: any;
}

const AutoCycleDiagram: React.FC<AutoCycleDiagramProps> = ({ 
  items, 
  centerLabel = 'ラベル',
  ...props 
}) => {
  // アイテムが3つ以下の場合は3つに、4つ以上の場合は4つに制限
  const limitedItems = items.slice(0, items.length <= 3 ? 3 : 4);
  while (limitedItems.length < (items.length <= 3 ? 3 : 4)) {
    limitedItems.push('XXX');
  }
  
  return (
    <CycleDiagram
      items={limitedItems}
      centerLabel={centerLabel}
      {...props}
    />
  );
};

export default AutoCycleDiagram; 