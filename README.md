# Spectacle Claude - AI駆動型スライド生成システム

議事録からプレゼンテーションスライドを自動生成するAIエージェントシステムです。

## 概要

Spectacle Claudeは、商談や会議の議事録を入力として受け取り、AIが自動的に構造化されたプレゼンテーション資料を生成するシステムです。LangGraphを使用した複数のAIエージェントが協調して動作し、最適なスライド構成とコンテンツを作成します。

## 主な機能

- **議事録解析**: AIが議事録から重要な情報を自動抽出
- **スライド構成の自動計画**: 内容に応じて最適なスライド構成を提案
- **豊富なスライドコンポーネント**: 20種類以上のスライドテンプレート
- **リアルタイムプレビュー**: 生成されたスライドを即座に確認
- **編集可能**: 生成後も自由にカスタマイズ可能

## 技術スタック

- **フロントエンド**: Next.js, React, TypeScript
- **スライド**: Spectacle
- **スタイリング**: Tailwind CSS
- **AIエージェント**: LangGraph, LangChain
- **LLM**: OpenAI GPT-4o

## AIエージェントのワークフロー

システムは3つのAIエージェントが順次実行される構成になっています：

### 1. AnalyzeNotesAgent（議事録分析エージェント）

**役割**: 入力された議事録を分析し、プレゼンテーション作成に必要な構造化データを抽出

**処理内容**:
- 議事録のテキストから以下の情報を抽出：
  - 会社名・クライアント名
  - 主要トピック・プロジェクト名
  - 現状の課題（カテゴリ、説明、影響度：high/medium/low）
  - 理想的な状態・目標
  - 提案された解決策
  - ROI情報（投資額、期待リターン、ROI率）
  - タイムライン（日付、マイルストーン）
  - 次のステップ（タスク、期限、優先度）

**エラーハンドリング**:
- JSON解析失敗時のフォールバック機能
- Zodスキーマによるデータバリデーション
- デフォルト値の自動設定

### 2. PlanSlidesAgent（スライド構成計画エージェント）

**役割**: 抽出された情報を基に、最適なスライド構成を決定

**スライド選定ロジック（ハイブリッド方式）**:

#### 必須スライド（CORE_SLIDES）
必ず含まれるスライド：
```typescript
const CORE_SLIDES = [
  {
    component: 'CoverSlide',
    slideType: 'cover',
    order: 1,
    description: '表紙・タイトルスライド'
  },
  {
    component: 'ExecutiveSummarySlide',
    slideType: 'summary',
    order: 2,
    description: 'エグゼクティブサマリー・要約'
  },
  {
    component: 'NextStepsSlide',
    slideType: 'next_steps',
    order: 999, // 最後に配置
    description: '次のステップ・アクションプラン'
  }
];
```

#### オプションスライド（OPTIONAL_SLIDES）
内容に応じてAIが自動選択するスライド：
```typescript
const OPTIONAL_SLIDES = [
  {
    component: 'ProblemAnalysisSlide',
    slideType: 'problem',
    order: 10,
    description: '課題分析・問題提起',
    conditions: ['課題が明確に特定されている', '問題解決型の提案']
  },
  {
    component: 'AsIsSlide',
    slideType: 'as_is',
    order: 15,
    description: '現状とあるべき姿の比較',
    conditions: ['現状と理想状態が明確', '変革・改善提案']
  },
  {
    component: 'SolutionOverviewSlide',
    slideType: 'solution',
    order: 20,
    description: 'ソリューション概要・解決策提示',
    conditions: ['具体的な解決策が提案されている', 'サービス・製品紹介']
  },
  {
    component: 'ROICalculationSlide',
    slideType: 'roi',
    order: 25,
    description: 'ROI計算・投資対効果',
    conditions: ['投資額と効果が数値化されている', '経営陣向け提案']
  },
  {
    component: 'TimelineSlide',
    slideType: 'timeline',
    order: 30,
    description: 'タイムライン・実施スケジュール',
    conditions: ['実施スケジュールが明確', 'プロジェクト提案']
  },
  {
    component: 'ComparisonTableSlide',
    slideType: 'comparison',
    order: 35,
    description: '比較表・選択肢比較',
    conditions: ['複数の選択肢を比較', '競合比較']
  },
  {
    component: 'MetricsSlide',
    slideType: 'metrics',
    order: 40,
    description: '指標・KPI・成果測定',
    conditions: ['KPIや指標が定義されている', '成果測定が重要']
  },
  {
    component: 'CaseInfoSlide',
    slideType: 'case_study',
    order: 45,
    description: '事例紹介・ケーススタディ',
    conditions: ['関連事例がある', '実績アピールが重要']
  },
  {
    component: 'VennDiagram',
    slideType: 'venn',
    order: 50,
    description: 'ベン図・関係性の可視化',
    conditions: ['複数要素の関係性を示す', '重複・交差を表現']
  },
  {
    component: 'AutoCycleDiagram',
    slideType: 'cycle',
    order: 55,
    description: 'サイクル図・プロセス図',
    conditions: ['循環的なプロセス', '継続的な改善サイクル']
  }
];
```

#### 選定アルゴリズム
1. **必須スライドの自動追加**: CORE_SLIDESは常に含める
2. **条件ベース選択**: 抽出された情報に基づいて、各オプションスライドの条件を評価
3. **重複チェック**: 既に選択されているコンポーネントの重複を防止
4. **order順ソート**: orderフィールドに基づいてスライド順序を決定
5. **妥当性検証**: 存在しないコンポーネントを除外
6. **長さ調整**: 5-8枚程度の適切な長さに調整

#### 検証・補正機能
```typescript
private validateAndCorrectStructure(structure: any[]): any[] {
  // 必須スライドの確認と追加
  const coreComponents = CORE_SLIDES.map(slide => slide.component);
  const existingComponents = structure.map(slide => slide.component);
  
  // 不足している必須スライドを追加
  const missingCoreSlides = coreComponents.filter(component => 
    !existingComponents.includes(component)
  );
  
  for (const missingComponent of missingCoreSlides) {
    // 必須スライドを自動追加
  }
  
  // order順でソート
  structure.sort((a, b) => a.order - b.order);
  
  // 存在しないコンポーネントを除外
  const validComponents = ALL_SLIDE_TEMPLATES.map(t => t.component);
  return structure.filter(slide => validComponents.includes(slide.component));
}
```

### 3. GenerateSlidesAgent（スライド生成エージェント）

**役割**: 決定されたスライド構成に基づいて、各スライドの具体的なコンテンツを生成

**処理内容**:
- 各スライドコンポーネント用のカスタマイズされたプロンプト生成
- コンポーネント固有のデータ形式に合わせたProps生成
- JSON形式での構造化データ出力
- 各スライドタイプに応じた適切なコンテンツ生成

**スライド固有の生成ロジック**:
- **ROICalculationSlide**: investment配列、returns配列、totalROI情報
- **ProblemAnalysisSlide**: problems配列、futureProjection情報
- **ExecutiveSummarySlide**: sections配列形式
- **AsIsSlide**: categories、asIs、toBe配列
- **SolutionOverviewSlide**: solutions配列、centralConcept
- **CoverSlide**: companyName、mainTitle、subTitle、date

### エラーハンドリング

各エージェントには堅牢なエラーハンドリングが実装されています：

1. **JSON解析エラー**: 複数の解析パターンを試行し、失敗時はフォールバック値を使用
2. **バリデーションエラー**: Zodスキーマによるデータ検証とデフォルト値の自動設定
3. **AIレスポンスエラー**: タイムアウト設定と再試行機能
4. **コンポーネントエラー**: 存在しないコンポーネントの自動除外

## ワークフロー実行フロー

```mermaid
graph TD
    A[議事録入力] --> B[AnalyzeNotesAgent]
    B --> C[情報抽出・構造化]
    C --> D[PlanSlidesAgent]
    D --> E[必須スライド追加]
    E --> F[条件評価によるオプションスライド選択]
    F --> G[スライド構成の検証・補正]
    G --> H[GenerateSlidesAgent]
    H --> I[各スライドのコンテンツ生成]
    I --> J[完成したプレゼンテーション]
```

## セットアップ

1. リポジトリをクローン
```bash
git clone [repository-url]
cd spectacle-claude
```

2. 依存関係をインストール
```bash
npm install
```

3. 環境変数を設定
```bash
# .env.localファイルを作成
OPENAI_API_KEY=your_openai_api_key
```

4. 開発サーバーを起動
```bash
npm run dev
```

## 使い方

1. ブラウザで `http://localhost:3000` を開く
2. 「AIスライドジェネレーター」をクリック
3. 議事録をテキストエリアに入力
4. 「スライドを生成」ボタンをクリック
5. 生成されたスライドをプレビュー

## スライドコンポーネント一覧

### 基本コンポーネント
- **CoverSlide**: 表紙スライド
- **ExecutiveSummarySlide**: エグゼクティブサマリー
- **ProblemAnalysisSlide**: 課題分析
- **AsIsSlide**: 現状とあるべき姿の比較
- **SolutionOverviewSlide**: ソリューション概要
- **ROICalculationSlide**: ROI計算
- **TimelineSlide**: タイムライン
- **ComparisonTableSlide**: 比較表
- **MetricsSlide**: 指標・KPI
- **NextStepsSlide**: 次のステップ

### 図表コンポーネント
- **VennDiagram**: ベン図
- **AutoCycleDiagram**: サイクル図
- **CaseInfoSlide**: 事例情報

## ディレクトリ構造

```
spectacle-claude/
├── app/                    # Next.jsアプリケーション
│   ├── page.tsx           # ホームページ
│   ├── generator/         # スライドジェネレーター
│   └── sample/            # サンプルプレゼンテーション
├── components/            # Reactコンポーネント
│   ├── SlideGenerator.tsx # メインジェネレーターUI
│   └── [各種スライドコンポーネント]
├── agents/                # AIエージェント
│   ├── graph.ts          # LangGraphワークフロー
│   ├── nodes/            # 各エージェントの実装
│   │   ├── analyzeNotes.ts    # 議事録分析エージェント
│   │   ├── planSlides.ts      # スライド構成計画エージェント
│   │   └── generateSlides.ts  # スライド生成エージェント
│   ├── templates/        # スライドテンプレート定義
│   │   └── slideTemplate.ts
│   ├── schemas.ts        # Zodバリデーションスキーマ
│   ├── types.ts          # TypeScript型定義
│   └── config.ts         # 設定ファイル
└── types/                # TypeScript型定義
```

## カスタマイズ

### 新しいスライドコンポーネントの追加

1. `components/`ディレクトリに新しいコンポーネントを作成
2. `types/slide-props.ts`に対応するprops型を定義
3. `agents/templates/slideTemplate.ts`のOPTIONAL_SLIDESに追加
4. `components/SlideGenerator.tsx`のslideComponentsに追加

### スライド選定ロジックのカスタマイズ

`agents/templates/slideTemplate.ts`でスライドテンプレートを編集：
- `conditions`配列を修正して選定条件を変更
- `order`を調整してスライド順序を変更
- 新しいテンプレートを追加してスライドの種類を拡張

### エージェントのカスタマイズ

各エージェントのプロンプトは`agents/nodes/`内の各ファイルで定義されています。必要に応じて調整してください。

## デバッグとモニタリング

- 各エージェントは詳細なログを出力
- フォールバック機能により処理継続を保証
- エラー情報はAgentStateに蓄積され、フロントエンドで確認可能

## ライセンス

MIT License
